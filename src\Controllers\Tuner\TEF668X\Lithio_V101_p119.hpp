#ifndef FMDX_TUNER_LITHIO_V101_P119_H
#define FMDX_TUNER_LITHIO_V101_P119_H

const uint8_t LITHIO_PATCH_V101[] PROGMEM =
{
    0xF0, 0x00, 0x38, 0x40, 0xD0, 0x80, 0x32, 0x81, 0x38, 0x58, 0xD0, 0x80, 0xF0, 0x00, 0x38, 0x52, 0xD0, 0x80, 0xF0, 0x00, 0x38, 0x64, 0xD0, 0x80,
    0xC4, 0xCB, 0x38, 0x68, 0xD0, 0x80, 0x80, 0x20, 0x38, 0xA9, 0xD0, 0x80, 0x90, 0x41, 0x38, 0xAE, 0xD0, 0x80, 0xF0, 0x00, 0x38, 0xB7, 0xD0, 0x80,
    0x80, 0x08, 0x38, 0xD8, 0xD0, 0x80, 0xF0, 0x00, 0x39, 0x2D, 0xD0, 0x80, 0xF0, 0x00, 0x3B, 0x55, 0xD0, 0x80, 0xF0, 0x00, 0x3B, 0x57, 0xD0, 0x80,
    0xC4, 0xA2, 0x01, 0x13, 0x60, 0x04, 0xF0, 0x00, 0x39, 0x01, 0xD0, 0x80, 0xF0, 0x00, 0x39, 0x5B, 0xD0, 0x80, 0xF0, 0x00, 0x3A, 0x35, 0xD0, 0x80,
    0xF0, 0x00, 0x39, 0x8A, 0xD0, 0x80, 0x40, 0x30, 0x39, 0x91, 0xD0, 0x80, 0xF0, 0x00, 0x19, 0x70, 0xD0, 0x80, 0x33, 0x22, 0x39, 0x96, 0xD0, 0x80,
    0xF0, 0x00, 0x39, 0xA5, 0xD0, 0x80, 0xAA, 0x10, 0x39, 0xA8, 0xD0, 0x80, 0xF0, 0x00, 0x39, 0xB6, 0xD0, 0x80, 0x90, 0x41, 0x39, 0xDC, 0xD0, 0x80,
    0xF0, 0x00, 0x39, 0xDF, 0xD0, 0x80, 0x31, 0x81, 0x39, 0xEE, 0xD0, 0x80, 0xF0, 0x00, 0x70, 0x00, 0xF0, 0x00, 0x26, 0x07, 0x3A, 0x13, 0xD0, 0x80,
    0x56, 0x02, 0x3A, 0x17, 0xD0, 0x80, 0xF0, 0x00, 0x70, 0x00, 0xD0, 0x08, 0xF0, 0x00, 0x25, 0x2A, 0xD0, 0x80, 0xF0, 0x00, 0x3A, 0x84, 0xD0, 0x80,
    0xF0, 0x00, 0x3A, 0x87, 0xD0, 0x80, 0xF0, 0x00, 0x3A, 0x96, 0xD0, 0x80, 0xF0, 0x00, 0x3A, 0x98, 0xD0, 0x80, 0xF0, 0x00, 0x3A, 0xAF, 0xD0, 0x80,
    0xF0, 0x00, 0x3A, 0xB2, 0xD0, 0x80, 0xF0, 0x00, 0x3A, 0xB5, 0xD0, 0x80, 0xF0, 0x00, 0x3A, 0xBB, 0xD0, 0x80, 0xF0, 0x00, 0x3A, 0xC5, 0xD0, 0x80,
    0xF0, 0x00, 0x3A, 0xD0, 0xD0, 0x80, 0xF0, 0x00, 0x3A, 0xDA, 0xD0, 0x80, 0xF0, 0x00, 0x3A, 0xDD, 0xD0, 0x80, 0x90, 0x00, 0x3A, 0xE1, 0xD0, 0x80,
    0xF0, 0x00, 0x3A, 0xE7, 0xD0, 0x80, 0xF0, 0x00, 0x3A, 0xEB, 0xD0, 0x80, 0xF0, 0x00, 0x3A, 0xF7, 0xD0, 0x80, 0xF0, 0x00, 0x3B, 0x02, 0xD0, 0x80,
    0xF0, 0x00, 0x3B, 0x0A, 0xD0, 0x80, 0x2B, 0xB0, 0x3B, 0x0D, 0xD0, 0x80, 0x8E, 0xD4, 0x08, 0xFB, 0x60, 0x03, 0x35, 0xC2, 0x08, 0xFB, 0x60, 0x06,
    0xF0, 0x00, 0x3B, 0x24, 0xD0, 0x80, 0x3C, 0xC3, 0x3B, 0x30, 0xD0, 0x80, 0xF0, 0x00, 0x3B, 0x3F, 0xD0, 0x80, 0xF0, 0x00, 0x3B, 0x52, 0xD0, 0x80,
    0xF0, 0x00, 0x3B, 0x58, 0xD0, 0x80, 0xF0, 0x00, 0x3B, 0x5B, 0xD0, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0xF0, 0x00, 0x70, 0x00, 0xA0, 0xB6, 0xF0, 0x00, 0x70, 0x00, 0xA0, 0x28, 0xF0, 0x00, 0x70, 0x00, 0xA0, 0xFB, 0xF0, 0x00, 0x70, 0x00, 0xA0, 0x6D,
    0xF0, 0x00, 0x70, 0x00, 0xA1, 0x5B, 0xF0, 0x00, 0x70, 0x00, 0xA1, 0xAE, 0xF0, 0x00, 0x70, 0x00, 0xA2, 0x39, 0xF0, 0x00, 0x70, 0x00, 0xA0, 0x04,
    0xF0, 0x00, 0x70, 0x00, 0xA2, 0x51, 0xF0, 0x00, 0x70, 0x00, 0xA2, 0xCF, 0xF0, 0x00, 0x70, 0x00, 0xA2, 0xB2, 0xF0, 0x00, 0x25, 0xAB, 0xD0, 0x80,
    0x08, 0x11, 0x60, 0x08, 0xF0, 0x00, 0x43, 0x10, 0x60, 0x00, 0xA3, 0x2C, 0xF0, 0x00, 0x3F, 0x80, 0xF0, 0x00, 0x0D, 0xD2, 0x60, 0x08, 0xF0, 0x00,
    0x26, 0x07, 0x60, 0x00, 0xA3, 0x29, 0xF0, 0x00, 0x30, 0x00, 0xD0, 0x08, 0xF0, 0x00, 0x03, 0x70, 0x60, 0x00, 0xF0, 0x00, 0x0D, 0xD2, 0x60, 0x0A,
    0x82, 0x40, 0x21, 0xE4, 0x60, 0x00, 0x26, 0x07, 0x60, 0x00, 0xEE, 0x00, 0xF0, 0x00, 0x07, 0xEF, 0x60, 0x08, 0x30, 0x20, 0x07, 0x08, 0xD0, 0x80,
    0xF0, 0x00, 0x07, 0xEF, 0x60, 0x08, 0xF0, 0x00, 0x03, 0xA2, 0xD2, 0x80, 0x40, 0x02, 0x08, 0x11, 0x60, 0x09, 0x00, 0xA8, 0x60, 0x02, 0xE6, 0x00,
    0x21, 0x11, 0x70, 0x00, 0xA0, 0x4F, 0x8C, 0x41, 0x21, 0x00, 0xA3, 0x1C, 0x82, 0x08, 0x32, 0x81, 0xF0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0xDC, 0x09,
    0x90, 0x40, 0x07, 0xBF, 0xD0, 0x80, 0xF0, 0x00, 0x0F, 0xD0, 0x60, 0x09, 0xD2, 0xFF, 0xC7, 0xFF, 0x60, 0x02, 0xF0, 0x00, 0x08, 0x21, 0xD0, 0x80,
    0x0D, 0xD2, 0x60, 0x0A, 0xA3, 0x15, 0xF0, 0x00, 0x20, 0x23, 0xF0, 0x00, 0x08, 0x00, 0x60, 0x0A, 0xA3, 0x13, 0x20, 0xA1, 0x08, 0x30, 0xD0, 0x80,
    0xCA, 0x09, 0x30, 0x23, 0xF0, 0x00, 0xC2, 0xCB, 0x08, 0x4E, 0xD0, 0x80, 0x40, 0x61, 0x1D, 0x26, 0x60, 0x09, 0x40, 0x92, 0x06, 0x17, 0x60, 0x0A,
    0x40, 0x13, 0x07, 0x91, 0x60, 0x0B, 0x42, 0x04, 0x07, 0x84, 0x60, 0x0C, 0x40, 0x85, 0x07, 0x88, 0x60, 0x0D, 0x37, 0x91, 0x1D, 0x51, 0x60, 0x0E,
    0x30, 0x22, 0xC0, 0x00, 0x60, 0x06, 0x30, 0x33, 0x1D, 0x57, 0x60, 0x0F, 0x31, 0x44, 0xC0, 0x00, 0x60, 0x07, 0x30, 0x55, 0x40, 0x00, 0xF0, 0x00,
    0x30, 0x66, 0x70, 0x00, 0xF0, 0x00, 0x30, 0xE0, 0x70, 0x00, 0xF0, 0x00, 0x31, 0x60, 0x70, 0x00, 0xF0, 0x00, 0x31, 0xE0, 0x70, 0x00, 0xF0, 0x00,
    0x32, 0x60, 0x70, 0x00, 0xF0, 0x00, 0x32, 0xE0, 0x70, 0x00, 0xF0, 0x00, 0x30, 0x77, 0x70, 0x00, 0xF0, 0x00, 0x30, 0xF0, 0x70, 0x00, 0xF0, 0x00,
    0x31, 0x70, 0x70, 0x00, 0xF0, 0x00, 0x31, 0xF0, 0x1D, 0x5D, 0x60, 0x09, 0x32, 0x70, 0x18, 0x00, 0x60, 0x01, 0x32, 0xF0, 0xE8, 0x00, 0x60, 0x02,
    0x30, 0x11, 0x70, 0x00, 0xF0, 0x00, 0x30, 0x92, 0x70, 0x00, 0xF0, 0x00, 0x31, 0x10, 0x70, 0x00, 0xF0, 0x00, 0x31, 0x90, 0x70, 0x00, 0xF0, 0x00,
    0x32, 0x10, 0x17, 0x07, 0x60, 0x08, 0x32, 0x90, 0x00, 0x00, 0x60, 0x01, 0x10, 0x00, 0x09, 0xBE, 0x60, 0x02, 0x10, 0x01, 0x02, 0x9E, 0x60, 0x03,
    0x10, 0x02, 0x08, 0x1D, 0x60, 0x04, 0x10, 0x03, 0x0B, 0x23, 0x60, 0x05, 0x10, 0x04, 0xE5, 0xD8, 0x60, 0x06, 0x10, 0x05, 0x05, 0xC3, 0x60, 0x07,
    0x10, 0x06, 0xF5, 0x9C, 0x60, 0x00, 0x10, 0x07, 0xE9, 0x50, 0x60, 0x01, 0x10, 0x00, 0x17, 0xE8, 0x60, 0x02, 0x10, 0x01, 0xEA, 0x8C, 0x60, 0x03,
    0x10, 0x02, 0xF2, 0x6E, 0x60, 0x04, 0x10, 0x03, 0xFE, 0x31, 0x60, 0x05, 0x10, 0x04, 0xF2, 0xD0, 0x60, 0x06, 0x10, 0x05, 0xF5, 0xC3, 0x60, 0x07,
    0x10, 0x06, 0xFF, 0xD8, 0x60, 0x00, 0x10, 0x07, 0x06, 0x0F, 0x60, 0x01, 0x10, 0x00, 0xF3, 0x19, 0x60, 0x02, 0x10, 0x01, 0x15, 0xD4, 0x60, 0x03,
    0x10, 0x02, 0x17, 0x7A, 0x60, 0x04, 0x10, 0x03, 0x14, 0x7B, 0x60, 0x05, 0x10, 0x04, 0x23, 0x7E, 0x60, 0x06, 0x10, 0x05, 0x30, 0x15, 0x60, 0x07,
    0x10, 0x06, 0x34, 0x26, 0x60, 0x00, 0x10, 0x07, 0x34, 0x2A, 0x60, 0x01, 0x10, 0x00, 0x4D, 0x01, 0x60, 0x02, 0x10, 0x01, 0x40, 0x3E, 0x60, 0x03,
    0x10, 0x02, 0x44, 0x34, 0x60, 0x04, 0x10, 0x03, 0x4F, 0x6D, 0x60, 0x05, 0x10, 0x04, 0x06, 0xAB, 0x60, 0x0D, 0x10, 0x05, 0x40, 0xA3, 0xF0, 0x00,
    0xF0, 0x00, 0x70, 0x00, 0x40, 0x64, 0xF0, 0x00, 0x30, 0x53, 0x46, 0x45, 0xF0, 0x00, 0x30, 0xD4, 0x41, 0xE6, 0xF0, 0x00, 0x31, 0x55, 0xF0, 0x00,
    0xF0, 0x00, 0x31, 0xD6, 0xD0, 0x08, 0x0D, 0xD3, 0x60, 0x09, 0xA2, 0xD0, 0xF0, 0x00, 0x20, 0x14, 0xA2, 0xCF, 0x80, 0x20, 0x0C, 0x46, 0xD0, 0x80,
    0x0D, 0xD3, 0x60, 0x0A, 0xA2, 0xCD, 0xF0, 0x00, 0x30, 0x22, 0xD0, 0x08, 0x7F, 0xFF, 0x60, 0x02, 0xE6, 0x00, 0xF0, 0x00, 0x70, 0x00, 0xF0, 0x00,
    0x37, 0x12, 0x0D, 0x21, 0xD0, 0x80, 0xF0, 0x00, 0x00, 0x6D, 0x60, 0x08, 0xF0, 0x00, 0x10, 0x00, 0x60, 0x00, 0xF0, 0x00, 0x07, 0x00, 0x60, 0x01,
    0x30, 0x00, 0x7F, 0xFF, 0x60, 0x02, 0x31, 0x01, 0x70, 0x00, 0xF0, 0x00, 0x31, 0x82, 0x70, 0x00, 0xD0, 0x08, 0xF0, 0x00, 0x01, 0x00, 0x60, 0x03,
    0xF0, 0x00, 0x0F, 0x25, 0xD2, 0x80, 0xF0, 0x00, 0x0F, 0x79, 0xD0, 0x80, 0xF0, 0x00, 0x01, 0x4E, 0x60, 0x03, 0xF0, 0x00, 0x10, 0x61, 0xD0, 0x80,
    0xF0, 0x00, 0x70, 0x00, 0xF0, 0x00, 0x90, 0x07, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x10, 0x16, 0xD1, 0x80, 0x40, 0x70, 0x0F, 0xEC, 0xD2, 0x80,
    0xF0, 0x00, 0x10, 0x10, 0x60, 0x03, 0xF0, 0x00, 0x00, 0xFF, 0x60, 0x01, 0x83, 0x5D, 0x70, 0x00, 0xF0, 0x00, 0xC3, 0x0C, 0x70, 0x00, 0x94, 0x09,
    0x40, 0x20, 0x0F, 0xEC, 0xD2, 0x80, 0x9F, 0x24, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0x90, 0x03, 0x40, 0x40, 0x0F, 0xEC, 0xD2, 0x80,
    0x40, 0x50, 0x0F, 0xEC, 0xD2, 0x80, 0xF0, 0x00, 0x10, 0x12, 0xD0, 0x80, 0x40, 0x40, 0x0F, 0xED, 0xD2, 0x80, 0x40, 0x50, 0x0F, 0xED, 0xD2, 0x80,
    0xF0, 0x00, 0x10, 0x12, 0xD0, 0x80, 0xF0, 0x00, 0x40, 0x20, 0xAF, 0xEC, 0x9F, 0x24, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0x90, 0x02,
    0x40, 0x40, 0x0F, 0xEC, 0xD2, 0x80, 0xF0, 0x00, 0x70, 0x00, 0x80, 0x01, 0x40, 0x40, 0x0F, 0xED, 0xD2, 0x80, 0x40, 0x50, 0x0F, 0xE6, 0xD2, 0x80,
    0xF0, 0x00, 0x0F, 0x61, 0x60, 0x09, 0x41, 0x73, 0x0F, 0x25, 0xD2, 0x80, 0x91, 0xC0, 0x0E, 0x27, 0x60, 0x09, 0x40, 0xB3, 0x0F, 0x25, 0xD0, 0x80,
    0x90, 0x82, 0x70, 0x00, 0x90, 0x11, 0xF0, 0x00, 0x70, 0x00, 0x90, 0x06, 0x4F, 0x90, 0x0F, 0xEA, 0xD2, 0x80, 0x4F, 0xF0, 0x0F, 0xEA, 0xD2, 0x80,
    0x0E, 0x26, 0x60, 0x09, 0xF0, 0x00, 0x00, 0xE4, 0x60, 0x03, 0xF0, 0x00, 0x00, 0x05, 0x60, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0x80, 0x05,
    0x4F, 0x90, 0x0F, 0xEB, 0xD2, 0x80, 0x4F, 0xF0, 0x0F, 0xEB, 0xD2, 0x80, 0x0E, 0x26, 0x60, 0x09, 0xF0, 0x00, 0x00, 0xA5, 0x60, 0x03, 0xF0, 0x00,
    0x00, 0x0D, 0x60, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x30, 0x13, 0xF0, 0x00, 0xFF, 0xE0, 0x60, 0x01, 0xF0, 0x00, 0x82, 0x92, 0x0F, 0x62, 0x60, 0x09,
    0xF0, 0x00, 0x0F, 0x21, 0xD2, 0x80, 0xF0, 0x00, 0x70, 0x00, 0x80, 0x02, 0x4F, 0x90, 0x0F, 0xE6, 0xD2, 0x80, 0x4F, 0xF0, 0x0F, 0xE6, 0xD2, 0x80,
    0x0E, 0x25, 0x60, 0x09, 0xF0, 0x00, 0x00, 0xE5, 0x60, 0x03, 0xA2, 0x8C, 0x91, 0xC7, 0x30, 0x13, 0xF0, 0x00, 0x00, 0xF1, 0x60, 0x00, 0xE6, 0x00,
    0x00, 0x53, 0x60, 0x00, 0xE2, 0x00, 0x0F, 0x64, 0x60, 0x09, 0xA2, 0x88, 0xF0, 0x00, 0x30, 0x10, 0xF0, 0x00, 0x82, 0x92, 0x0F, 0x63, 0x60, 0x09,
    0x00, 0x12, 0x60, 0x00, 0xF0, 0x00, 0xFF, 0xE0, 0x60, 0x01, 0xF0, 0x00, 0xF0, 0x00, 0x0F, 0x21, 0xD0, 0x80, 0xF0, 0x00, 0x08, 0x58, 0x60, 0x08,
    0x7F, 0xFF, 0x60, 0x03, 0xA2, 0x81, 0x33, 0x03, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x00, 0xE0, 0x60, 0x00, 0x0D, 0xD4, 0x60, 0x08, 0xA2, 0x7E,
    0xF0, 0x00, 0x30, 0x00, 0xF0, 0x00, 0x30, 0x80, 0x70, 0x00, 0xD0, 0x08, 0x08, 0x4C, 0x60, 0x09, 0xF0, 0x00, 0x82, 0x49, 0x70, 0x00, 0xF0, 0x00,
    0xF0, 0x00, 0x33, 0x11, 0xD0, 0x08, 0x00, 0x00, 0x60, 0x03, 0xA2, 0x78, 0x82, 0xD3, 0x70, 0x00, 0xF0, 0x00, 0x90, 0x40, 0x70, 0x00, 0x94, 0x03,
    0x08, 0x96, 0x60, 0x04, 0xA2, 0x75, 0x91, 0x1D, 0x70, 0x00, 0xF0, 0x00, 0x91, 0x26, 0x70, 0x00, 0x80, 0x18, 0x00, 0x04, 0x60, 0x03, 0xA2, 0x72,
    0x82, 0xD3, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0x94, 0x03, 0x08, 0x4C, 0x60, 0x04, 0xA2, 0x6F, 0x93, 0x05, 0x70, 0x00, 0xF0, 0x00,
    0x93, 0x0E, 0x70, 0x00, 0x80, 0x12, 0x00, 0x20, 0x60, 0x03, 0xA2, 0x6C, 0x82, 0xD3, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0x94, 0x03,
    0x08, 0x65, 0x60, 0x04, 0xA2, 0x69, 0x91, 0x0D, 0x70, 0x00, 0xF0, 0x00, 0x91, 0x16, 0x70, 0x00, 0x80, 0x0C, 0x00, 0xE0, 0x60, 0x03, 0xA2, 0x66,
    0x82, 0xD3, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0x94, 0x03, 0x08, 0x96, 0x60, 0x04, 0xA2, 0x63, 0x91, 0x1D, 0x70, 0x00, 0xF0, 0x00,
    0x91, 0x26, 0x70, 0x00, 0x80, 0x06, 0x00, 0xF0, 0x60, 0x03, 0xA2, 0x60, 0x82, 0xD3, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0xD4, 0x09,
    0x08, 0x56, 0x60, 0x0A, 0xA2, 0x5D, 0x20, 0x25, 0x70, 0x00, 0xF0, 0x00, 0x20, 0xA6, 0x70, 0x00, 0x80, 0x00, 0x00, 0x21, 0x60, 0x03, 0xA2, 0x5A,
    0x82, 0xC3, 0x0D, 0xD4, 0x60, 0x09, 0xF0, 0x00, 0x70, 0x00, 0x94, 0x04, 0x08, 0x6F, 0x60, 0x08, 0xA2, 0x57, 0xF0, 0x00, 0x30, 0x12, 0xF0, 0x00,
    0xF0, 0x00, 0x33, 0x05, 0xF0, 0x00, 0xF0, 0x00, 0x33, 0x86, 0xD0, 0x08, 0x00, 0x80, 0x60, 0x03, 0xA2, 0x53, 0x82, 0xC3, 0x0D, 0xD4, 0x60, 0x09,
    0xF0, 0x00, 0x70, 0x00, 0xD4, 0x09, 0x08, 0x58, 0x60, 0x08, 0xF0, 0x00, 0xF0, 0x00, 0x30, 0x92, 0xF0, 0x00, 0xF0, 0x00, 0x35, 0x05, 0xF0, 0x00,
    0xF0, 0x00, 0x35, 0x86, 0xD0, 0x08, 0x20, 0x16, 0x70, 0x00, 0x94, 0x03, 0x20, 0x97, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x33, 0xC6, 0xF0, 0x00,
    0xF0, 0x00, 0x34, 0x47, 0xF0, 0x00, 0x0D, 0xD4, 0x60, 0x09, 0xA2, 0x48, 0xF0, 0x00, 0x20, 0x11, 0xA2, 0x47, 0x82, 0x59, 0x70, 0x00, 0xF0, 0x00,
    0xF0, 0x00, 0x70, 0x00, 0x94, 0x03, 0x08, 0x6F, 0x60, 0x08, 0xA2, 0x44, 0xF0, 0x00, 0x33, 0x06, 0xF0, 0x00, 0xF0, 0x00, 0x33, 0x87, 0xF0, 0x00,
    0xF0, 0x00, 0x20, 0x91, 0xA2, 0x41, 0x82, 0x59, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0xD4, 0x09, 0x08, 0x58, 0x60, 0x08, 0xA2, 0x3E,
    0xF0, 0x00, 0x35, 0x06, 0xF0, 0x00, 0xF0, 0x00, 0x35, 0x87, 0xD0, 0x08, 0x17, 0xCB, 0x60, 0x07, 0xF0, 0x00, 0x00, 0x07, 0x60, 0x01, 0xF0, 0x00,
    0x40, 0xD0, 0x70, 0x00, 0xA0, 0x0F, 0x18, 0x18, 0x60, 0x06, 0xF0, 0x00, 0x90, 0x40, 0x05, 0x9F, 0xD2, 0x80, 0x18, 0x22, 0x60, 0x06, 0xF0, 0x00,
    0xF0, 0x00, 0x05, 0x9F, 0xD2, 0x80, 0x18, 0x2C, 0x60, 0x06, 0xF0, 0x00, 0xF0, 0x00, 0x05, 0x9F, 0xD2, 0x80, 0x17, 0xDC, 0x60, 0x07, 0xF0, 0x00,
    0x00, 0x35, 0x60, 0x01, 0xF0, 0x00, 0x43, 0xB0, 0x70, 0x00, 0xA0, 0x06, 0x18, 0x1B, 0x60, 0x06, 0xF0, 0x00, 0x90, 0x40, 0x05, 0x9F, 0xD2, 0x80,
    0x18, 0x25, 0x60, 0x06, 0xF0, 0x00, 0xF0, 0x00, 0x05, 0x9F, 0xD2, 0x80, 0x18, 0x2F, 0x60, 0x06, 0xF0, 0x00, 0xF0, 0x00, 0x05, 0x9F, 0xD0, 0x80,
    0xD7, 0xCA, 0x00, 0xFF, 0x60, 0x04, 0x81, 0xD7, 0x0D, 0xCA, 0x60, 0x09, 0xD0, 0x56, 0x70, 0x00, 0xF0, 0x00, 0x82, 0x76, 0x30, 0x17, 0xF0, 0x00,
    0xD0, 0xF6, 0x40, 0x83, 0xF0, 0x00, 0xC1, 0xA4, 0x20, 0x19, 0xF0, 0x00, 0x82, 0xF6, 0x70, 0x00, 0xF0, 0x00, 0xC1, 0x80, 0x20, 0x17, 0xA2, 0x22,
    0xC3, 0xE7, 0x70, 0x00, 0xF0, 0x00, 0xC5, 0xC7, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x30, 0x17, 0xD0, 0x08, 0x39, 0x82, 0x60, 0x00, 0xA2, 0x1E,
    0x80, 0x08, 0x70, 0x00, 0xA2, 0x1D, 0x9F, 0xF3, 0x70, 0x00, 0xD0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0xD0, 0x08, 0xF0, 0x00, 0x28, 0x50, 0xD2, 0x80,
    0xF0, 0x00, 0x70, 0x00, 0x80, 0x01, 0xF0, 0x00, 0x28, 0x4C, 0xD2, 0x80, 0xF0, 0x00, 0x26, 0x66, 0xD2, 0x80, 0x40, 0x10, 0x29, 0x19, 0xD2, 0x80,
    0xF0, 0x00, 0x70, 0x00, 0xA1, 0xEF, 0xF0, 0x00, 0x70, 0x00, 0xA0, 0x72, 0xF0, 0x00, 0x70, 0x00, 0xAF, 0x97, 0xF0, 0x00, 0x17, 0x84, 0xD2, 0x80,
    0xF0, 0x00, 0x2A, 0x1F, 0xD2, 0x80, 0x9F, 0xF3, 0x70, 0x00, 0xF0, 0x00, 0x40, 0x21, 0x28, 0xAB, 0xD5, 0x80, 0xF0, 0x00, 0x28, 0xDE, 0xD0, 0x80,
    0xF0, 0x00, 0x06, 0x50, 0xD5, 0x80, 0xF0, 0x00, 0x26, 0x68, 0xD2, 0x80, 0x40, 0x10, 0x29, 0x19, 0xD2, 0x80, 0xF0, 0x00, 0x70, 0x00, 0xA0, 0x68,
    0xF0, 0x00, 0x28, 0xDF, 0xD0, 0x80, 0xF0, 0x00, 0x06, 0x50, 0xD5, 0x80, 0xF0, 0x00, 0x26, 0x66, 0xD2, 0x80, 0x40, 0x10, 0x29, 0x19, 0xD2, 0x80,
    0xF0, 0x00, 0x70, 0x00, 0xA0, 0x63, 0xF0, 0x00, 0x28, 0xE0, 0xD0, 0x80, 0xF0, 0x00, 0x06, 0x50, 0xD5, 0x80, 0xF0, 0x00, 0x26, 0x68, 0xD2, 0x80,
    0x40, 0x10, 0x29, 0x19, 0xD2, 0x80, 0xF0, 0x00, 0x70, 0x00, 0xA0, 0x5E, 0xF0, 0x00, 0x28, 0xE1, 0xD0, 0x80, 0xF0, 0x00, 0x06, 0x50, 0xD5, 0x80,
    0xF0, 0x00, 0x00, 0x08, 0x60, 0x00, 0xF0, 0x00, 0x28, 0x4F, 0xD2, 0x80, 0xF0, 0x00, 0x27, 0x8A, 0xD1, 0x80, 0xF0, 0x00, 0x26, 0x69, 0xD2, 0x80,
    0x90, 0x02, 0x26, 0x66, 0xD2, 0x80, 0xF0, 0x00, 0x27, 0x8A, 0xD0, 0x80, 0xF0, 0x00, 0x70, 0x00, 0x8F, 0xDB, 0xF0, 0x00, 0x70, 0x00, 0x8F, 0xDB,
    0xF0, 0x00, 0x70, 0x00, 0x8F, 0xDC, 0xF0, 0x00, 0x70, 0x00, 0x8F, 0xE6, 0xF0, 0x00, 0x70, 0x00, 0x8F, 0xEA, 0xF0, 0x00, 0x70, 0x00, 0x8F, 0xEE,
    0x40, 0x14, 0x06, 0x4E, 0xD0, 0x80, 0xF0, 0x00, 0x70, 0x00, 0x8F, 0xF1, 0xF0, 0x00, 0x26, 0x6E, 0xD2, 0x80, 0x40, 0x10, 0x29, 0x19, 0xD2, 0x80,
    0xF0, 0x00, 0x70, 0x00, 0xA1, 0xC7, 0xF0, 0x00, 0x70, 0x00, 0xA0, 0x4A, 0xF0, 0x00, 0x70, 0x00, 0xAF, 0x6F, 0xF0, 0x00, 0x17, 0x84, 0xD2, 0x80,
    0xF0, 0x00, 0x06, 0x8E, 0xD0, 0x80, 0x42, 0x71, 0x16, 0xE0, 0xD2, 0x80, 0x00, 0x00, 0x60, 0x01, 0xF0, 0x00, 0x01, 0x90, 0x60, 0x02, 0xF0, 0x00,
    0xF0, 0x00, 0x20, 0xBB, 0xD2, 0x80, 0x40, 0x11, 0x20, 0xB7, 0xD0, 0x80, 0xF0, 0x00, 0x0D, 0xD7, 0x60, 0x0C, 0x23, 0x10, 0x60, 0x05, 0xA1, 0xE2,
    0x30, 0x45, 0x23, 0x16, 0x60, 0x03, 0xF0, 0x00, 0x41, 0x8E, 0x60, 0x04, 0x36, 0x33, 0x70, 0x00, 0xF0, 0x00, 0x36, 0xB4, 0x70, 0x00, 0xF0, 0x00,
    0xF0, 0x00, 0x1B, 0xE0, 0xD2, 0x80, 0xF0, 0x00, 0x0A, 0x18, 0x60, 0x08, 0xFD, 0x40, 0x60, 0x00, 0xA1, 0xDB, 0x30, 0x00, 0x1B, 0xD5, 0xD0, 0x80,
    0x40, 0x50, 0x0C, 0xC0, 0x60, 0x08, 0x2B, 0x00, 0x60, 0x01, 0xF0, 0x00, 0x30, 0x00, 0x70, 0x00, 0xF0, 0x00, 0x3B, 0x81, 0x23, 0x12, 0xD2, 0x80,
    0xF0, 0x00, 0x70, 0x00, 0x80, 0xCE, 0xF0, 0x00, 0x24, 0xEB, 0xD2, 0x80, 0xF0, 0x00, 0x70, 0x00, 0xA0, 0x12, 0xF0, 0x00, 0x1C, 0x93, 0xD0, 0x80,
    0xB1, 0xE8, 0x26, 0x93, 0xF0, 0x00, 0xBA, 0x11, 0x2F, 0x92, 0xF0, 0x00, 0x82, 0x18, 0x70, 0x00, 0xF0, 0x00, 0xA2, 0x10, 0x27, 0x14, 0xF0, 0x00,
    0x80, 0x18, 0x70, 0x00, 0xF0, 0x00, 0x82, 0x61, 0x36, 0x90, 0xF0, 0x00, 0xA2, 0x51, 0x70, 0x00, 0xF0, 0x00, 0x80, 0x61, 0x09, 0xA9, 0x60, 0x08,
    0xF0, 0x00, 0x37, 0x11, 0xF0, 0x00, 0xF0, 0x00, 0x21, 0x98, 0xD2, 0x80, 0xF0, 0x00, 0x0A, 0x7B, 0x60, 0x08, 0xF0, 0x00, 0x23, 0xAA, 0xD2, 0x80,
    0x0A, 0x6B, 0x60, 0x08, 0xA0, 0x82, 0xF0, 0x00, 0x1C, 0xFE, 0xD0, 0x80, 0xF0, 0x00, 0x09, 0xC4, 0x60, 0x07, 0xF0, 0x00, 0x65, 0xC1, 0x60, 0x06,
    0xF0, 0x00, 0x1D, 0x64, 0xD0, 0x80, 0xF0, 0x00, 0x09, 0x26, 0x60, 0x0D, 0xF0, 0x00, 0x09, 0x2E, 0x60, 0x0E, 0xF0, 0x00, 0x0C, 0xA1, 0x60, 0x08,
    0xB0, 0x03, 0x04, 0xA8, 0x60, 0x0F, 0x22, 0x06, 0x0C, 0x78, 0x60, 0x0A, 0x24, 0x83, 0x0C, 0x80, 0x60, 0x0B, 0x91, 0x86, 0x00, 0x70, 0x40, 0x17,
    0x23, 0x89, 0x1E, 0x03, 0xD5, 0x80, 0x23, 0x01, 0x70, 0x00, 0xCD, 0x48, 0xA1, 0xD8, 0x00, 0x22, 0xF0, 0x00, 0x82, 0x0D, 0x70, 0x00, 0xF0, 0x00,
    0x86, 0x0F, 0x70, 0x00, 0xF0, 0x00, 0xA3, 0x7E, 0x70, 0x00, 0xF0, 0x00, 0x91, 0x40, 0x70, 0x00, 0x9C, 0x02, 0x91, 0xC7, 0x7F, 0xFF, 0x60, 0x05,
    0xF0, 0x00, 0x58, 0x05, 0xEA, 0x00, 0x90, 0x44, 0x70, 0x00, 0x58, 0x07, 0xA1, 0x51, 0x10, 0x60, 0xF0, 0x00, 0xA1, 0xCA, 0x7F, 0xFF, 0x60, 0x07,
    0xA8, 0xF3, 0x00, 0x32, 0xF0, 0x00, 0xC2, 0xFB, 0x1D, 0xF1, 0xD2, 0x80, 0xA2, 0x10, 0x10, 0x51, 0xF0, 0x00, 0x85, 0x07, 0x70, 0x00, 0x21, 0x06,
    0x81, 0x02, 0x70, 0x00, 0xF0, 0x00, 0x8F, 0xBD, 0x70, 0x00, 0x21, 0x84, 0x81, 0xB2, 0x70, 0x00, 0xE0, 0xC0, 0x8F, 0xE5, 0x00, 0x70, 0x40, 0x17,
    0x81, 0x22, 0x58, 0x06, 0xE0, 0xC0, 0x89, 0x12, 0x18, 0x12, 0xF0, 0x00, 0xB1, 0x92, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x1D, 0xE0, 0xD0, 0x80,
    0x40, 0x00, 0x09, 0xAD, 0x60, 0x08, 0x4F, 0xF1, 0x09, 0x86, 0x60, 0x09, 0xF0, 0x00, 0x31, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x3E, 0x91, 0x80, 0x87,
    0x7F, 0xFF, 0x60, 0x02, 0xE6, 0x00, 0xF0, 0x00, 0x70, 0x00, 0xF0, 0x00, 0x37, 0x12, 0x20, 0xB0, 0xD0, 0x80, 0x3C, 0x01, 0xC6, 0xA8, 0x60, 0x00,
    0x35, 0x01, 0x00, 0x48, 0x60, 0x05, 0xA0, 0x18, 0x70, 0x00, 0xF0, 0x00, 0xAB, 0xB0, 0x0C, 0x6E, 0x60, 0x09, 0x80, 0x28, 0x03, 0xE9, 0x60, 0x05,
    0xF0, 0x00, 0x33, 0x10, 0xF0, 0x00, 0x8F, 0x2D, 0x0C, 0x52, 0x60, 0x08, 0xF0, 0x00, 0x70, 0x00, 0x94, 0x02, 0x40, 0x45, 0xB1, 0x5C, 0x60, 0x06,
    0x46, 0x66, 0x60, 0x07, 0x80, 0x02, 0x40, 0x65, 0xAA, 0xCE, 0x60, 0x06, 0xC6, 0x66, 0x60, 0x07, 0x80, 0x00, 0xA1, 0xA0, 0x70, 0x00, 0x43, 0x84,
    0xAF, 0x48, 0x70, 0x00, 0x41, 0xD5, 0xF0, 0x00, 0x21, 0x34, 0xD0, 0x80, 0xF0, 0x00, 0xBE, 0x77, 0x60, 0x03, 0xF0, 0x00, 0x4B, 0x00, 0x60, 0x04,
    0xA0, 0xD0, 0x70, 0x00, 0xF0, 0x00, 0xAA, 0x42, 0x70, 0x00, 0xF0, 0x00, 0x80, 0xA2, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x3B, 0x82, 0xD0, 0x08,
    0xF0, 0x00, 0x0C, 0xC0, 0x60, 0x0A, 0xF0, 0x00, 0xFF, 0xFF, 0x60, 0x04, 0xF0, 0x00, 0x2B, 0x00, 0x60, 0x05, 0x3C, 0x24, 0x0A, 0x7B, 0x60, 0x0B,
    0x3C, 0xA4, 0x06, 0x40, 0x60, 0x06, 0x3B, 0xA5, 0x0C, 0xAD, 0x60, 0x0C, 0x30, 0x36, 0x00, 0x04, 0x60, 0x07, 0xF0, 0x00, 0xAD, 0x84, 0x60, 0x06,
    0x32, 0x47, 0x00, 0x42, 0x60, 0x05, 0x32, 0xC6, 0x0B, 0x5E, 0x60, 0x04, 0x31, 0x45, 0x01, 0xFD, 0x60, 0x03, 0x31, 0xC4, 0x0C, 0x67, 0x60, 0x0D,
    0x33, 0x43, 0x03, 0xD7, 0x60, 0x00, 0xF0, 0x00, 0x4D, 0x78, 0x60, 0x01, 0x31, 0x50, 0xF6, 0xB4, 0x60, 0x02, 0x32, 0xD1, 0x0C, 0x59, 0x60, 0x0E,
    0x33, 0x52, 0x00, 0x05, 0x60, 0x03, 0x40, 0x17, 0x40, 0x02, 0x60, 0x04, 0x32, 0x63, 0xFF, 0xF8, 0x60, 0x05, 0x32, 0xE4, 0x0C, 0x6E, 0x60, 0x0F,
    0x33, 0x65, 0x00, 0xA4, 0x60, 0x00, 0x52, 0x06, 0xD9, 0x24, 0x60, 0x01, 0x31, 0x70, 0x00, 0x3A, 0x60, 0x02, 0x32, 0xF1, 0x0D, 0xD8, 0x60, 0x08,
    0x33, 0x72, 0x08, 0xD7, 0x60, 0x09, 0x30, 0x07, 0x7F, 0xFF, 0x60, 0x05, 0x33, 0x16, 0x0C, 0x3B, 0x60, 0x0A, 0x82, 0xDB, 0x35, 0x15, 0x40, 0x74,
    0xF0, 0x00, 0x1E, 0x7E, 0x60, 0x0B, 0xF0, 0x00, 0x3F, 0x24, 0xF0, 0x00, 0xF0, 0x00, 0x30, 0x33, 0xD0, 0x08, 0xF0, 0x00, 0x0D, 0xD6, 0x60, 0x09,
    0x91, 0xC7, 0x70, 0x00, 0xF0, 0x00, 0x20, 0x10, 0x22, 0x80, 0xE6, 0x40, 0xF0, 0x00, 0x21, 0xE3, 0xD0, 0x80, 0x20, 0x11, 0x0D, 0xD8, 0x60, 0x08,
    0xA2, 0x82, 0x09, 0xB5, 0x60, 0x0B, 0x20, 0x07, 0x2B, 0xFD, 0x60, 0x04, 0xF0, 0x00, 0xAD, 0x1D, 0x60, 0x05, 0x9F, 0xFB, 0x39, 0x1D, 0x60, 0x06,
    0x9F, 0xF3, 0x70, 0x00, 0x90, 0x04, 0x7F, 0xFF, 0x60, 0x03, 0x90, 0x0E, 0xA2, 0x41, 0x7F, 0xFF, 0x60, 0x03, 0xF0, 0x00, 0x30, 0x21, 0xF0, 0x00,
    0xF0, 0x00, 0x3F, 0xB3, 0xD0, 0x08, 0xA3, 0x0B, 0x30, 0x22, 0xF0, 0x00, 0xA2, 0x4A, 0x70, 0x00, 0xF0, 0x00, 0xA3, 0x50, 0x70, 0x00, 0xF0, 0x00,
    0x80, 0xC3, 0x70, 0x00, 0xF0, 0x00, 0xA2, 0x8A, 0x70, 0x00, 0xF0, 0x00, 0xA3, 0x90, 0xFD, 0x95, 0x60, 0x07, 0x80, 0xC3, 0x07, 0xFF, 0x60, 0x04,
    0x80, 0xFB, 0x70, 0x00, 0xF0, 0x00, 0x8A, 0xE3, 0x70, 0x00, 0xF0, 0x00, 0xD9, 0x1B, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x3F, 0xB3, 0xD0, 0x08,
    0x30, 0x22, 0x69, 0x25, 0x60, 0x04, 0x3F, 0xB3, 0xF8, 0x0A, 0x60, 0x05, 0xA1, 0x08, 0x70, 0x00, 0xF0, 0x00, 0xAA, 0x14, 0x70, 0x00, 0xF0, 0x00,
    0x81, 0x2C, 0x09, 0x36, 0x60, 0x0C, 0xA3, 0x04, 0x09, 0x3D, 0x60, 0x0D, 0x90, 0x04, 0x70, 0x00, 0xE1, 0x40, 0xF0, 0x00, 0x32, 0xC4, 0xF0, 0x00,
    0xF0, 0x00, 0x32, 0xD4, 0xD0, 0x08, 0x0D, 0xD8, 0x60, 0x08, 0xA1, 0x44, 0xF0, 0x00, 0x30, 0x01, 0xD0, 0x08, 0xF0, 0x00, 0x0D, 0xD7, 0x60, 0x0F,
    0xF0, 0x00, 0x09, 0x22, 0x60, 0x0E, 0x20, 0x7C, 0x0A, 0x6B, 0x60, 0x08, 0x20, 0x60, 0x70, 0x00, 0xF0, 0x00, 0x20, 0xE1, 0x70, 0x00, 0xF0, 0x00,
    0x26, 0x09, 0x1B, 0x33, 0x60, 0x0A, 0x18, 0x40, 0x70, 0x00, 0xF0, 0x00, 0x18, 0x41, 0x70, 0x00, 0xF0, 0x00, 0xA0, 0x03, 0x00, 0x21, 0x08, 0x10,
    0x00, 0x21, 0x08, 0x10, 0xC0, 0x0A, 0xA0, 0x09, 0x00, 0x21, 0x08, 0x10, 0xA0, 0x09, 0x30, 0x7C, 0xF0, 0x00, 0xA4, 0x0A, 0x7A, 0x9D, 0xF0, 0x00,
    0xF0, 0x00, 0x36, 0x09, 0xF0, 0x00, 0x26, 0x89, 0x1B, 0x33, 0x60, 0x0A, 0xF0, 0x00, 0x37, 0x02, 0xF0, 0x00, 0xA0, 0x03, 0x00, 0x21, 0x08, 0x10,
    0x00, 0x21, 0x08, 0x10, 0xC0, 0x0A, 0xA0, 0x09, 0x00, 0x21, 0x08, 0x10, 0xA0, 0x09, 0x7B, 0x9D, 0xF0, 0x00, 0xA4, 0x0A, 0x7A, 0x9D, 0xF0, 0x00,
    0xF0, 0x00, 0x36, 0x89, 0xF0, 0x00, 0x37, 0x82, 0x23, 0x84, 0xD0, 0x80, 0xF0, 0x00, 0x0C, 0x52, 0x60, 0x08, 0xF0, 0x00, 0x1E, 0x7E, 0x60, 0x0F,
    0x22, 0x80, 0x0C, 0x6E, 0x60, 0x09, 0x23, 0x01, 0x0C, 0x67, 0x60, 0x0A, 0x22, 0x92, 0x0C, 0x4B, 0x60, 0x0B, 0x23, 0x13, 0x30, 0xF0, 0xF0, 0x00,
    0x21, 0x24, 0x31, 0x71, 0xF0, 0x00, 0x21, 0xA5, 0x31, 0xF2, 0xF0, 0x00, 0x21, 0x36, 0x32, 0x73, 0xF0, 0x00, 0x21, 0xB7, 0x32, 0xF4, 0xF0, 0x00,
    0x33, 0x75, 0x00, 0x00, 0x60, 0x00, 0x33, 0xF6, 0x60, 0x00, 0x60, 0x01, 0x34, 0x77, 0xC0, 0xA4, 0x60, 0x02, 0x32, 0x80, 0x00, 0xF5, 0x60, 0x03,
    0x33, 0x01, 0x07, 0x00, 0x60, 0x04, 0x32, 0x92, 0x07, 0x10, 0x60, 0x05, 0x33, 0x13, 0x07, 0x00, 0x60, 0x06, 0x31, 0x24, 0x07, 0x10, 0x60, 0x07,
    0x31, 0xA5, 0x40, 0x10, 0xF0, 0x00, 0x31, 0x36, 0x70, 0x00, 0xF0, 0x00, 0x31, 0xB7, 0x30, 0x70, 0xD0, 0x08, 0xF0, 0x00, 0x1E, 0x7E, 0x60, 0x0F,
    0xF0, 0x00, 0x0C, 0x52, 0x60, 0x08, 0x20, 0x77, 0x0C, 0x6E, 0x60, 0x09, 0x20, 0xF0, 0x0C, 0x67, 0x60, 0x0A, 0x91, 0xC7, 0x0C, 0x4B, 0x60, 0x0B,
    0x21, 0x71, 0x70, 0x00, 0xD0, 0x09, 0x21, 0xF2, 0x32, 0x80, 0xF0, 0x00, 0x22, 0x73, 0x33, 0x01, 0xF0, 0x00, 0x22, 0xF4, 0x32, 0x92, 0xF0, 0x00,
    0x23, 0x75, 0x33, 0x13, 0xF0, 0x00, 0x23, 0xF6, 0x31, 0x24, 0xF0, 0x00, 0x24, 0x77, 0x31, 0xA5, 0xF0, 0x00, 0x82, 0x00, 0x31, 0x36, 0xF0, 0x00,
    0x30, 0x70, 0x31, 0xB7, 0xD0, 0x08, 0x0D, 0xD6, 0x60, 0x08, 0xA1, 0x08, 0xF0, 0x00, 0x30, 0x07, 0xD0, 0x08, 0xF0, 0x00, 0x0C, 0x4B, 0x60, 0x08,
    0xF0, 0x00, 0x00, 0xE5, 0x60, 0x00, 0xF0, 0x00, 0x00, 0x04, 0x60, 0x02, 0x31, 0x00, 0x55, 0xE9, 0x60, 0x03, 0x32, 0x02, 0xF6, 0x65, 0x60, 0x04,
    0x32, 0x83, 0x0C, 0x52, 0x60, 0x09, 0x33, 0x04, 0x00, 0x1D, 0x60, 0x00, 0xF0, 0x00, 0x00, 0x38, 0x60, 0x01, 0x31, 0x10, 0x00, 0x0B, 0x60, 0x02,
    0x31, 0x91, 0x28, 0x4A, 0x60, 0x03, 0x32, 0x12, 0xD6, 0xF9, 0x60, 0x04, 0x32, 0x93, 0x70, 0x00, 0xF0, 0x00, 0x33, 0x14, 0x70, 0x00, 0xD0, 0x08,
    0x40, 0x66, 0x0C, 0xD8, 0x60, 0x08, 0x40, 0xA7, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x33, 0x06, 0xF0, 0x00, 0xF0, 0x00, 0x31, 0x87, 0xD0, 0x08,
    0x22, 0x93, 0x25, 0x36, 0xD2, 0x80, 0xF0, 0x00, 0x25, 0x66, 0xD2, 0x80, 0xF0, 0x00, 0x25, 0x5E, 0xD0, 0x80, 0x22, 0x13, 0x25, 0x36, 0xD2, 0x80,
    0xF0, 0x00, 0x25, 0x66, 0xD0, 0x80, 0xF0, 0x00, 0x40, 0x11, 0x40, 0x60, 0xF0, 0x00, 0x40, 0xF3, 0x40, 0x12, 0x40, 0x64, 0x0C, 0xD8, 0x60, 0x08,
    0xF0, 0x00, 0x70, 0x00, 0x80, 0x04, 0xF0, 0x00, 0x40, 0x11, 0x40, 0x60, 0xF0, 0x00, 0x40, 0xA3, 0x40, 0x12, 0x40, 0x44, 0x0C, 0xD8, 0x60, 0x08,
    0xF0, 0x00, 0x70, 0x00, 0x80, 0x00, 0xF0, 0x00, 0x33, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x32, 0x81, 0xF0, 0x00, 0xF0, 0x00, 0x32, 0x02, 0xF0, 0x00,
    0xF0, 0x00, 0x31, 0x83, 0xF0, 0x00, 0xF0, 0x00, 0x31, 0x04, 0xD0, 0x08, 0x40, 0x20, 0x05, 0x10, 0xD2, 0x80, 0xF0, 0x00, 0x70, 0x00, 0x8F, 0xF5,
    0x40, 0x30, 0x05, 0x10, 0xD2, 0x80, 0xF0, 0x00, 0x70, 0x00, 0x8F, 0xEF, 0x40, 0x30, 0x0D, 0x2F, 0x60, 0x08, 0xF0, 0x00, 0x27, 0x12, 0x60, 0x01,
    0x10, 0x00, 0x00, 0x24, 0x60, 0x00, 0x10, 0x01, 0x27, 0x11, 0x60, 0x01, 0x10, 0x00, 0x00, 0x14, 0x60, 0x00, 0x10, 0x01, 0x0D, 0x1F, 0x60, 0x09,
    0x10, 0x00, 0x3A, 0xC9, 0x60, 0x02, 0xF0, 0x00, 0x3A, 0xD3, 0x60, 0x03, 0x31, 0x92, 0x0D, 0xD9, 0x60, 0x08, 0x34, 0x93, 0x22, 0x04, 0x60, 0x00,
    0xF0, 0x00, 0x10, 0x10, 0x60, 0x01, 0x30, 0x00, 0x0D, 0xDB, 0x60, 0x09, 0x30, 0x81, 0x22, 0x04, 0x60, 0x00, 0xF0, 0x00, 0x10, 0x10, 0x60, 0x01,
    0x30, 0x10, 0x0D, 0x13, 0x60, 0x08, 0x30, 0x91, 0x3A, 0xC1, 0x60, 0x00, 0xF0, 0x00, 0x3A, 0xC4, 0x60, 0x01, 0x30, 0x80, 0x00, 0x00, 0x60, 0x00,
    0x31, 0x81, 0x70, 0x00, 0xF0, 0x00, 0x32, 0x00, 0x70, 0x00, 0xF0, 0x00, 0x32, 0x80, 0x70, 0x00, 0xD0, 0x08, 0xF0, 0x00, 0x04, 0x55, 0xD2, 0x80,
    0xF0, 0x00, 0x70, 0x00, 0xAD, 0xA7, 0xF0, 0x00, 0x27, 0x2A, 0xD0, 0x80, 0xF0, 0x00, 0x24, 0x3A, 0xD2, 0x80, 0xF0, 0x00, 0x25, 0x2A, 0xD2, 0x80,
    0xF0, 0x00, 0x27, 0x40, 0xD0, 0x80, 0xF0, 0x00, 0x08, 0x9D, 0xD2, 0x80, 0x3A, 0xB8, 0x60, 0x00, 0xF0, 0x00, 0x40, 0x21, 0x26, 0x60, 0xD0, 0x80,
    0x40, 0x11, 0x13, 0xE7, 0xD2, 0x80, 0x27, 0x55, 0x60, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x26, 0x55, 0xD0, 0x80, 0xF0, 0x00, 0x1B, 0xFA, 0xD2, 0x80,
    0x3A, 0xBE, 0x60, 0x00, 0xF0, 0x00, 0x40, 0x11, 0x26, 0x60, 0xD0, 0x80, 0x40, 0x11, 0x13, 0xE7, 0xD2, 0x80, 0xF0, 0x00, 0x27, 0xA5, 0x60, 0x00,
    0xF0, 0x00, 0x26, 0x55, 0xD0, 0x80, 0xF0, 0x00, 0x27, 0xAA, 0xD2, 0x80, 0xF0, 0x00, 0x70, 0x00, 0xAF, 0x8B, 0xF0, 0x00, 0x25, 0x5E, 0xD0, 0x80,
    0xF0, 0x00, 0x70, 0x00, 0x8F, 0x9E, 0x3A, 0xC7, 0x60, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x26, 0x55, 0xD0, 0x80, 0x27, 0xE7, 0x60, 0x00, 0xF0, 0x00,
    0x40, 0x11, 0x26, 0x60, 0xD0, 0x80, 0xF0, 0x00, 0x25, 0x2E, 0xD2, 0x80, 0xF0, 0x00, 0x1C, 0x13, 0xD2, 0x80, 0xF0, 0x00, 0x24, 0x39, 0xD2, 0x80,
    0xF0, 0x00, 0x1B, 0x75, 0xD2, 0x80, 0xF0, 0x00, 0x1F, 0x99, 0xD2, 0x80, 0xF0, 0x00, 0x0F, 0x89, 0xD2, 0x80, 0xF0, 0x00, 0x26, 0x8A, 0xD0, 0x80,
    0xF0, 0x00, 0x25, 0x2A, 0xD2, 0x80, 0xF0, 0x00, 0x24, 0xE0, 0xD2, 0x80, 0xF0, 0x00, 0x28, 0x1A, 0xD0, 0x80, 0xF0, 0x00, 0x25, 0x2E, 0xD2, 0x80,
    0xF0, 0x00, 0x1C, 0x13, 0xD2, 0x80, 0xF0, 0x00, 0x24, 0x39, 0xD2, 0x80, 0xF0, 0x00, 0x1B, 0x75, 0xD2, 0x80, 0xF0, 0x00, 0x1F, 0x99, 0xD2, 0x80,
    0xF0, 0x00, 0x0F, 0x89, 0xD2, 0x80, 0xF0, 0x00, 0x26, 0x8A, 0xD0, 0x80, 0xF0, 0x00, 0x25, 0x2A, 0xD2, 0x80, 0xF0, 0x00, 0x24, 0xE0, 0xD2, 0x80,
    0xF0, 0x00, 0x28, 0x42, 0xD0, 0x80, 0x0D, 0xD9, 0x60, 0x08, 0xA0, 0x9C, 0xF0, 0x00, 0x20, 0x04, 0xF0, 0x00, 0x20, 0x85, 0x70, 0x00, 0xAD, 0xDC,
    0xF0, 0x00, 0x12, 0x40, 0xD0, 0x80, 0x40, 0xA7, 0x41, 0x17, 0xE6, 0x40, 0xF0, 0x00, 0x70, 0x00, 0xAF, 0x8E, 0xF0, 0x00, 0x25, 0x49, 0xD2, 0x80,
    0xF0, 0x00, 0x0E, 0xB8, 0xD2, 0x80, 0xF0, 0x00, 0x21, 0xE8, 0xD5, 0x80, 0xF0, 0x00, 0x21, 0xE6, 0xD0, 0x80, 0x0D, 0xDB, 0x60, 0x08, 0xA0, 0x92,
    0xF0, 0x00, 0x20, 0x04, 0xF0, 0x00, 0x20, 0x85, 0x70, 0x00, 0xAD, 0xD2, 0xF0, 0x00, 0x12, 0x40, 0xD0, 0x80, 0xF0, 0x00, 0x21, 0x00, 0xA0, 0x8E,
    0x90, 0x00, 0x70, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x0E, 0x8A, 0xD5, 0x80, 0xF0, 0x00, 0x0E, 0x88, 0xD0, 0x80, 0x0D, 0xD9, 0x60, 0x08, 0xA0, 0x03,
    0x4F, 0xF1, 0x28, 0x5D, 0xD0, 0x80, 0x0D, 0xDB, 0x60, 0x08, 0xA0, 0x01, 0x4F, 0xF1, 0x28, 0x73, 0xD0, 0x80, 0x9F, 0xAE, 0x70, 0x00, 0xF0, 0x00,
    0xF0, 0x00, 0x70, 0x00, 0xD4, 0x09, 0xF0, 0x00, 0x30, 0x02, 0xF0, 0x00, 0xF0, 0x00, 0x30, 0x83, 0xD0, 0x08, 0xF0, 0x00, 0x25, 0xC1, 0xD2, 0x80,
    0xF0, 0x00, 0x28, 0xC2, 0xD0, 0x80, 0x0D, 0x7F, 0x60, 0x08, 0xF0, 0x00, 0x02, 0x9D, 0x60, 0x00, 0xA0, 0x7F, 0xF0, 0x00, 0x30, 0x00, 0xF0, 0x00,
    0xF0, 0x00, 0x04, 0x24, 0xD0, 0x80, 0xF0, 0x00, 0x0D, 0x5D, 0x60, 0x08, 0xF0, 0x00, 0x00, 0x73, 0x60, 0x00, 0xF0, 0x00, 0x00, 0xE1, 0x60, 0x01,
    0xF0, 0x00, 0x39, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x39, 0x81, 0xD0, 0x08, 0xF0, 0x00, 0x00, 0x2C, 0xD2, 0x80, 0xAB, 0xF8, 0x2B, 0x30, 0xF0, 0x00,
    0xAA, 0x86, 0x00, 0x2C, 0xD2, 0x80, 0xAB, 0xF8, 0x33, 0xB6, 0xF0, 0x00, 0xAB, 0x98, 0x00, 0x3A, 0xD2, 0x80, 0x34, 0x21, 0x34, 0x10, 0xF0, 0x00,
    0xF0, 0x00, 0x2C, 0xB0, 0xA0, 0x71, 0xD0, 0x41, 0x70, 0x00, 0x80, 0x0E, 0x40, 0xF0, 0x02, 0xAB, 0xD2, 0x80, 0x40, 0xB0, 0x02, 0xA8, 0xD2, 0x80,
    0xF0, 0x00, 0x01, 0x47, 0xD0, 0x80, 0x40, 0x02, 0x0E, 0x8B, 0x60, 0x08, 0xFF, 0xC0, 0x60, 0x01, 0xF0, 0x00, 0xF0, 0x00, 0x02, 0xA4, 0xD2, 0x80,
    0xF0, 0x00, 0x0E, 0x8C, 0x60, 0x08, 0x00, 0x03, 0x60, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x02, 0xA8, 0xD2, 0x80, 0xF0, 0x00, 0x0E, 0x9B, 0x60, 0x08,
    0x82, 0x00, 0x02, 0xA2, 0xD2, 0x80, 0xF0, 0x00, 0x2C, 0xB1, 0xF0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0xA0, 0x01, 0xF0, 0x00, 0x01, 0x79, 0xD0, 0x80,
    0x90, 0x40, 0x01, 0x85, 0xD0, 0x80, 0xF0, 0x00, 0x0E, 0xBA, 0xD2, 0x80, 0x05, 0x00, 0x60, 0x09, 0xD0, 0x09, 0x1D, 0x91, 0x60, 0x0A, 0xF0, 0x00,
    0x0B, 0x60, 0x60, 0x01, 0xF0, 0x00, 0x5B, 0x06, 0x60, 0x02, 0xA0, 0x5C, 0x32, 0x22, 0x32, 0x11, 0xF0, 0x00, 0x00, 0x00, 0x60, 0x01, 0xF0, 0x00,
    0x59, 0xD8, 0x60, 0x02, 0xF0, 0x00, 0x82, 0x00, 0x0D, 0xDD, 0x60, 0x08, 0x30, 0x22, 0x30, 0x11, 0xF0, 0x00, 0xF0, 0x00, 0x30, 0x00, 0xD0, 0x08,
    0x0D, 0xDD, 0x60, 0x0B, 0xA0, 0x06, 0xF0, 0x00, 0x70, 0x00, 0x94, 0x03, 0x0D, 0xDE, 0x60, 0x0B, 0xA0, 0x04, 0xF0, 0x00, 0x70, 0x00, 0x94, 0x01,
    0xF0, 0x00, 0x70, 0x00, 0x80, 0x05, 0xF0, 0x00, 0x03, 0x42, 0xD2, 0x80, 0x21, 0xC6, 0x03, 0x3B, 0xD0, 0x80, 0xF0, 0x00, 0x70, 0x00, 0xF0, 0x00,
    0xF0, 0x00, 0x20, 0x36, 0xA0, 0x4D, 0x91, 0x86, 0x70, 0x00, 0xD0, 0x08, 0xF0, 0x00, 0x00, 0x40, 0xD2, 0x80, 0xF0, 0x00, 0x03, 0x2E, 0xD0, 0x80,
    0xF0, 0x00, 0x1C, 0x6C, 0x60, 0x08, 0xF0, 0x00, 0x26, 0xC1, 0xF0, 0x00, 0xF0, 0x00, 0x00, 0x02, 0xA0, 0x47, 0x90, 0x82, 0x70, 0x00, 0xF0, 0x00,
    0x82, 0x8A, 0x70, 0x00, 0x90, 0x03, 0x90, 0x8A, 0x70, 0x00, 0x90, 0x01, 0xF0, 0x00, 0x70, 0x00, 0x8F, 0xFB, 0x82, 0x80, 0x70, 0x00, 0xF0, 0x00,
    0x1C, 0x9B, 0x60, 0x08, 0xA0, 0x41, 0xF0, 0x00, 0x00, 0x02, 0xA0, 0x40, 0x90, 0x82, 0x70, 0x00, 0xF0, 0x00, 0x82, 0x8A, 0x40, 0x07, 0x90, 0x02,
    0xF0, 0x00, 0x40, 0x17, 0x90, 0x01, 0xF0, 0x00, 0x70, 0x00, 0x8F, 0xFB, 0xF0, 0x00, 0x70, 0x00, 0x8D, 0x22, 0xF0, 0x00, 0x1C, 0xA8, 0x60, 0x0D,
    0xF0, 0x00, 0x21, 0x40, 0xF0, 0x00, 0xF0, 0x00, 0x00, 0x51, 0xA0, 0x38, 0x90, 0x41, 0x70, 0x00, 0xF0, 0x00, 0x82, 0x41, 0x40, 0x07, 0x90, 0x02,
    0xF0, 0x00, 0x40, 0x17, 0x90, 0x01, 0xF0, 0x00, 0x70, 0x00, 0x8F, 0xFB, 0xF0, 0x00, 0x70, 0x00, 0xAD, 0x1A, 0x4F, 0xF6, 0x24, 0xC2, 0x60, 0x01,
    0x40, 0x03, 0x0D, 0xDE, 0x60, 0x0B, 0x82, 0x09, 0x3F, 0xFF, 0x60, 0x02, 0x40, 0x05, 0x44, 0x03, 0xE2, 0x00, 0x40, 0x17, 0x1C, 0x8C, 0x60, 0x0D,
    0x30, 0x33, 0x70, 0x00, 0xF0, 0x00, 0x9F, 0x7D, 0x00, 0x51, 0xA0, 0x2C, 0xC2, 0x53, 0x70, 0x00, 0xF0, 0x00, 0x82, 0xC4, 0x03, 0x7C, 0xD1, 0x80,
    0xF0, 0x00, 0x70, 0x00, 0x97, 0xFC, 0x91, 0x46, 0x03, 0x80, 0xD0, 0x80, 0xF0, 0x00, 0x70, 0x00, 0x93, 0x9C, 0xF0, 0x00, 0x70, 0x00, 0x8F, 0x9D,
    0xF0, 0x00, 0x70, 0x00, 0x8F, 0xA4, 0x0D, 0xDD, 0x60, 0x08, 0xA0, 0x24, 0xF0, 0x00, 0x30, 0x02, 0xD0, 0x08, 0xF0, 0x00, 0x03, 0xBC, 0xD0, 0x80,
    0xF0, 0x00, 0x2A, 0x3A, 0xD2, 0x80, 0xF0, 0x00, 0x3B, 0x7B, 0xD2, 0x80, 0xF0, 0x00, 0x29, 0xA4, 0xD0, 0x80, 0xF0, 0x00, 0x00, 0xA4, 0x60, 0x00,
    0x0E, 0x91, 0x60, 0x08, 0xA0, 0x1B, 0xF0, 0x00, 0x03, 0xA1, 0x60, 0x00, 0x0E, 0x23, 0x60, 0x08, 0xA0, 0x19, 0xF0, 0x00, 0x01, 0x80, 0x60, 0x00,
    0x0E, 0x9B, 0x60, 0x08, 0xA0, 0x17, 0x41, 0x40, 0x29, 0xFD, 0xD2, 0x80, 0xF0, 0x00, 0x00, 0x08, 0x60, 0x00, 0x0F, 0xE6, 0x60, 0x08, 0xA0, 0x14,
    0xF0, 0x00, 0x01, 0x04, 0x60, 0x00, 0xF0, 0x00, 0x29, 0xFD, 0xD2, 0x80, 0x0F, 0xE6, 0x60, 0x08, 0xA0, 0x12, 0x90, 0x00, 0x0D, 0xCA, 0x60, 0x09,
    0xF0, 0x00, 0x70, 0x00, 0x97, 0xFB, 0x0F, 0xE5, 0x60, 0x08, 0xA0, 0x0F, 0x90, 0x04, 0x30, 0x10, 0xF0, 0x00, 0x0F, 0xE4, 0x60, 0x08, 0xA0, 0x0D,
    0x90, 0x05, 0x30, 0x90, 0xF0, 0x00, 0x91, 0x01, 0x01, 0x4A, 0x60, 0x00, 0x91, 0x42, 0x29, 0xFC, 0xD5, 0x80, 0x83, 0x42, 0x70, 0x00, 0x93, 0xF2,
    0xF0, 0x00, 0x29, 0xFC, 0xD9, 0x80, 0xF0, 0x00, 0x00, 0x00, 0x60, 0x00, 0x0E, 0x9B, 0x60, 0x08, 0xA0, 0x05, 0xF0, 0x00, 0x00, 0x92, 0x60, 0x00,
    0x0E, 0x91, 0x60, 0x08, 0xA0, 0x03, 0xF0, 0x00, 0x00, 0xCF, 0x60, 0x00, 0x0E, 0x6A, 0x60, 0x08, 0xA0, 0x01, 0xF0, 0x00, 0x29, 0xE1, 0xD0, 0x80,
    0xF0, 0x00, 0x2A, 0x02, 0xD0, 0x80, 0xF0, 0x00, 0x2A, 0x0B, 0xD0, 0x80, 0xF0, 0x00, 0x70, 0x00, 0xD0, 0x08, 0xF0, 0x00, 0x1C, 0x6C, 0x60, 0x08,
    0xF0, 0x00, 0x01, 0xCC, 0x60, 0x00, 0xF0, 0x00, 0x01, 0xCD, 0x60, 0x00, 0x10, 0x00, 0x01, 0xE1, 0x60, 0x00, 0x10, 0x00, 0x01, 0xEA, 0x60, 0x00,
    0x10, 0x00, 0x01, 0xEB, 0x60, 0x00, 0x10, 0x00, 0x01, 0xF5, 0x60, 0x00, 0x10, 0x00, 0x02, 0xA9, 0x60, 0x00, 0x10, 0x00, 0x02, 0xF4, 0x60, 0x00,
    0x10, 0x00, 0x02, 0xF5, 0x60, 0x00, 0x10, 0x00, 0x03, 0x01, 0x60, 0x00, 0x10, 0x00, 0x03, 0x02, 0x60, 0x00, 0x10, 0x00, 0x03, 0x4F, 0x60, 0x00,
    0x10, 0x00, 0x03, 0x66, 0x60, 0x00, 0x10, 0x00, 0x03, 0x9B, 0x60, 0x00, 0x10, 0x00, 0x03, 0xFD, 0x60, 0x00, 0x10, 0x00, 0x04, 0x6E, 0x60, 0x00,
    0x10, 0x00, 0x04, 0x6F, 0x60, 0x00, 0x10, 0x00, 0x04, 0xBA, 0x60, 0x00, 0x10, 0x00, 0x05, 0x06, 0x60, 0x00, 0x10, 0x00, 0x05, 0x1C, 0x60, 0x00,
    0x10, 0x00, 0x06, 0x9D, 0x60, 0x00, 0x10, 0x00, 0x07, 0x29, 0x60, 0x00, 0x10, 0x00, 0x07, 0xAB, 0x60, 0x00, 0x10, 0x00, 0x07, 0xAC, 0x60, 0x00,
    0x10, 0x00, 0x08, 0x6E, 0x60, 0x00, 0x10, 0x00, 0x09, 0xB7, 0x60, 0x00, 0x10, 0x00, 0x0A, 0x17, 0x60, 0x00, 0x10, 0x00, 0x0A, 0x3A, 0x60, 0x00,
    0x10, 0x00, 0x0A, 0x9F, 0x60, 0x00, 0x10, 0x00, 0x0A, 0xBD, 0x60, 0x00, 0x10, 0x00, 0x0B, 0x27, 0x60, 0x00, 0x10, 0x00, 0x00, 0x00, 0x60, 0x00,
    0xF0, 0x00, 0x10, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x10, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x1C, 0x8C, 0x60, 0x08, 0xF0, 0x00, 0x43, 0xCA, 0x60, 0x00,
    0xF0, 0x00, 0x46, 0x68, 0x60, 0x00, 0x10, 0x00, 0x9E, 0x64, 0x60, 0x00, 0x10, 0x00, 0xA0, 0x6C, 0x60, 0x00, 0x10, 0x00, 0x21, 0xC5, 0x60, 0x00,
    0x10, 0x00, 0xA4, 0xC2, 0x60, 0x00, 0x10, 0x00, 0xE6, 0xC0, 0x60, 0x00, 0x10, 0x00, 0xE6, 0xC5, 0x60, 0x00, 0x10, 0x00, 0xA8, 0x41, 0x60, 0x00,
    0x10, 0x00, 0xA8, 0x55, 0x60, 0x00, 0x10, 0x00, 0xA8, 0x5A, 0x60, 0x00, 0x10, 0x00, 0x28, 0xDC, 0x60, 0x00, 0x10, 0x00, 0x29, 0x9A, 0x60, 0x00,
    0x10, 0x00, 0x29, 0xA9, 0x60, 0x00, 0x10, 0x00, 0x00, 0x00, 0x60, 0x00, 0xF0, 0x00, 0x10, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x10, 0x00, 0xF0, 0x00,
    0xF0, 0x00, 0x1C, 0x9B, 0x60, 0x08, 0xF0, 0x00, 0x01, 0xEC, 0x60, 0x00, 0xF0, 0x00, 0x02, 0x12, 0x60, 0x00, 0x10, 0x00, 0x02, 0x41, 0x60, 0x00,
    0x10, 0x00, 0x02, 0x5D, 0x60, 0x00, 0x10, 0x00, 0x02, 0x8E, 0x60, 0x00, 0x10, 0x00, 0x02, 0xDB, 0x60, 0x00, 0x10, 0x00, 0x03, 0x9C, 0x60, 0x00,
    0x10, 0x00, 0x03, 0xC2, 0x60, 0x00, 0x10, 0x00, 0x04, 0x82, 0x60, 0x00, 0x10, 0x00, 0x05, 0x51, 0x60, 0x00, 0x10, 0x00, 0x05, 0x52, 0x60, 0x00,
    0x10, 0x00, 0x06, 0xCC, 0x60, 0x00, 0x10, 0x00, 0x00, 0x00, 0x60, 0x00, 0xF0, 0x00, 0x10, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x10, 0x00, 0xF0, 0x00,
    0xF0, 0x00, 0x1C, 0xA8, 0x60, 0x08, 0xF0, 0x00, 0x02, 0xF2, 0x60, 0x00, 0xF0, 0x00, 0x02, 0xF3, 0x60, 0x00, 0x10, 0x00, 0x02, 0xF4, 0x60, 0x00,
    0x10, 0x00, 0x02, 0xF5, 0x60, 0x00, 0x10, 0x00, 0x02, 0xF6, 0x60, 0x00, 0x10, 0x00, 0x02, 0xF7, 0x60, 0x00, 0x10, 0x00, 0x02, 0xF8, 0x60, 0x00,
    0x10, 0x00, 0x00, 0x00, 0x60, 0x00, 0xF0, 0x00, 0x10, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x10, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0x70, 0x00, 0xD0, 0x08
};

const uint8_t LITHIO_LUT_V101[] PROGMEM =
{
    0x40, 0x13, 0x46, 0xEA, 0x47, 0x07, 0x48, 0x2E,
    0x48, 0x4D, 0x4C, 0x45, 0x4D, 0x20, 0x4F, 0x78,
    0x50, 0x33, 0x53, 0xC7, 0x54, 0x0F, 0x54, 0x10,
    0x45, 0xF8, 0x46, 0x23, 0x46, 0x2F, 0x46, 0x73,
    0x46, 0x8D, 0x56, 0xA0, 0x59, 0x6D, 0x5B, 0xD0,
    0x5C, 0x92, 0x5C, 0xF7, 0x5D, 0x62, 0x60, 0xAF,
    0x61, 0x26, 0x61, 0x4A, 0x61, 0x9F, 0x61, 0xE0,
    0x61, 0xED, 0x65, 0x19, 0x65, 0x29, 0x65, 0x30,
    0x65, 0x34, 0x66, 0x02, 0x66, 0x20, 0x67, 0x29,
    0x67, 0x3F, 0x67, 0x52, 0x67, 0xA2, 0x67, 0xE6,
    0x68, 0x19, 0x68, 0x41, 0x68, 0x6A, 0x68, 0x6C,
    0x68, 0x80, 0x68, 0x8C, 0x68, 0xBB, 0x40, 0xF4,
    0x41, 0x46, 0x41, 0x74, 0x42, 0xE0, 0x42, 0xEB,
    0x43, 0x2D, 0x43, 0x75, 0x43, 0x7A, 0x43, 0xCE,
    0x69, 0xA3, 0x69, 0xC0
};

const size_t LITHIO_PATCH_V101_LEN = sizeof(LITHIO_PATCH_V101);
const size_t LITHIO_LUT_V101_LEN = sizeof(LITHIO_LUT_V101);

#endif
