name: Arduino Build
on: [push, pull_request]

jobs:
  build:
    strategy:
      matrix:
        arduino-platform: ["arduino:avr", "esp32:esp32"]
        include:
          - arduino-platform: "arduino:avr"
            fqbn: "arduino:avr:diecimila"
          - arduino-platform: "esp32:esp32"
            fqbn: "esp32:esp32:esp32"

    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@master

      - name: Setup Arduino CLI
        uses: arduino/setup-arduino-cli@v1.1.2

      - name: Create work directory
        run: |
          mkdir "$HOME/Arduino"
          ln -s "$PWD" "$HOME/Arduino/FM-DX-Tuner"
      - name: Install platform
        run: |
          arduino-cli core update-index
          arduino-cli core install ${{ matrix.arduino-platform }}

      - name: Compile Sketch
        run: arduino-cli compile --fqbn ${{ matrix.fqbn }} "$HOME/Arduino/FM-DX-Tuner" --warnings more
