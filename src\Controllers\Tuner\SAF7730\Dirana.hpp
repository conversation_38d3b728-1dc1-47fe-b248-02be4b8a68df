/*  SPDX-License-Identifier: GPL-3.0-or-later
 *
 *  FM-DX Tuner
 *  Copyright (C) 2023  <PERSON> 
 *
 *  This program is free software; you can redistribute it and/or
 *  modify it under the terms of the GNU General Public License
 *  as published by the Free Software Foundation; either version 3
 *  of the License, or (at your option) any later version.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 */

#ifndef FMDX_TUNER_DIRANA_H
#define FMDX_TUNER_DIRANA_H

#define DIRANA_DSP_I2C   0x38
#define DIRANA_IF_I2C    0xC4

#define DIRANA_DSP_FALSE                0x000
#define DIRANA_DSP_TRUE                 0x7FF

#define DIRANA_IDENTIFICATION           0x000000
#define DIRANA_RDS_STATUS               0x000030
#define DIRANA_RDS_DATA                 0x000031
#define DIRANA_RDS_CONTROL              0x000035

#define DIRANA_FIR_MEMORY               0x010C00
#define DIRANA_FIR_FM_ADAPTIVE_1        0x0101A1
#define DIRANA_FIR_FM_FIXED_1           0x0101A2
#define DIRANA_FIR_FM_MODE_1            0x0101A3
#define DIRANA_FIR_FM_ADAPTIVE_2        0x0101A4
#define DIRANA_FIR_FM_FIXED_2           0x0101A5
#define DIRANA_FIR_FM_MODE_2            0x0101A6
#define DIRANA_FIR_AM_FIXED_1           0x0100E7
#define DIRANA_FIR_AM_ATT_1             0x0100E8
#define DIRANA_FIR_AM_FIXED_2           0x05021B
#define DIRANA_FIR_AM_ATT_2             0x05021C

#define DIRANA_ULTRASONIC_NOISE         0x0100D2
#define DIRANA_FM_LEVEL_FAST            0x030092
#define DIRANA_FM_LEVEL                 0x030097
#define DIRANA_FM_MULTIPATH             0x030099
#define DIRANA_AM_LEVEL                 0x03006E
#define DIRANA_VOLUME_SCALER            0x031011
#define DIRANA_FORCE_MONO               0x031020
#define DIRANA_PILOT                    0x03102C
#define DIRANA_PILOT_THRESHOLD          0x03128D

#define DIRANA_MAX_VOLUME 2047

/* DSP initialization data */
const uint8_t DIRANA_INIT[] PROGMEM =
{
    6, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
    5, 0x03, 0x10, 0x09, 0x00, 0x00,
    6, 0x00, 0x00, 0x20, 0x02, 0x09, 0x7A,
    6, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
    6, 0x01, 0x0F, 0xFD, 0x00, 0x00, 0x00,
    6, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
    6, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
    6, 0x03, 0x0F, 0xFD, 0x00, 0x00, 0x00,
    6, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
    6, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
    6, 0x05, 0x0F, 0xFD, 0x00, 0x00, 0x00,
    6, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
    6, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
    6, 0x09, 0x0F, 0xFD, 0x00, 0x00, 0x00,
    6, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
    6, 0x00, 0x00, 0x10, 0x00, 0x02, 0x00,
    6, 0x09, 0x0F, 0xFD, 0x00, 0x00, 0x00,
    6, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
    6, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
    6, 0x0D, 0x0F, 0xFD, 0x00, 0x00, 0x00,
    6, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00,
    6, 0x09, 0x01, 0x5F, 0x80, 0x00, 0x00,
    6, 0x09, 0x01, 0x60, 0x80, 0x00, 0x00,
    6, 0x09, 0x01, 0x61, 0x80, 0x00, 0x00,
    6, 0x01, 0x00, 0x04, 0x00, 0x00, 0x01,
    6, 0x0D, 0x0F, 0xF6, 0x01, 0xB6, 0xDB,
    6, 0x0D, 0x0F, 0xF7, 0x00, 0xC0, 0x3E,
    6, 0x01, 0x0F, 0xF9, 0x57, 0xF3, 0x55,
    6, 0x00, 0x00, 0x20, 0x02, 0x09, 0x7A,
    6, 0x05, 0x0F, 0xF9, 0x00, 0x00, 0x13,
//  5, 0x03, 0x12, 0x8D, 0x00, 0x52, // stereo pilot threshold 3.75 kHz
    5, 0x01, 0x10, 0x04, 0x00, 0x00,
    5, 0x05, 0x10, 0x48, 0x00, 0x00,
    5, 0x03, 0x10, 0x11, 0x07, 0xFF,
    5, 0x0D, 0x14, 0x88, 0x05, 0x09,
    5, 0x03, 0x12, 0x3B, 0x05, 0x2C,
    5, 0x03, 0x11, 0x26, 0x00, 0xF6,
    5, 0x03, 0x11, 0x27, 0x0F, 0xEB,
    5, 0x05, 0x10, 0x19, 0x00, 0xF6,
    5, 0x05, 0x10, 0x1A, 0x0F, 0xD9,
    5, 0x03, 0x11, 0x38, 0x0A, 0x00,
    5, 0x03, 0x12, 0x7A, 0x00, 0xF6,
    5, 0x03, 0x12, 0x7B, 0x0F, 0xD9,
    5, 0x05, 0x10, 0x86, 0x00, 0xE8,
    5, 0x05, 0x10, 0x87, 0x0F, 0xC5,
    5, 0x01, 0x10, 0x95, 0x00, 0x80,
    5, 0x01, 0x10, 0x96, 0x00, 0x00,
    5, 0x01, 0x10, 0x97, 0x00, 0x00,
    5, 0x03, 0x11, 0xFD, 0x07, 0xFE,
    5, 0x03, 0x11, 0xFE, 0x00, 0x01,
    5, 0x03, 0x12, 0x29, 0x05, 0xB6,
    6, 0x01, 0x01, 0xD1, 0x00, 0x10, 0x22,
    6, 0x01, 0x01, 0xD2, 0x00, 0x00, 0xE5,
    7, 0x03, 0x12, 0x25, 0x06, 0x34, 0x07, 0xFE,
    7, 0x03, 0x12, 0x27, 0x00, 0xC4, 0x00, 0x01,
    5, 0x05, 0x10, 0xC2, 0x00, 0x80,
    5, 0x05, 0x10, 0xC3, 0x00, 0x00,
    5, 0x05, 0x10, 0xC4, 0x00, 0x00,
    5, 0x05, 0x10, 0xAA, 0x05, 0xB6,
    6, 0x05, 0x02, 0xA1, 0x00, 0x10, 0x22,
    6, 0x05, 0x02, 0xA2, 0x00, 0x00, 0xE5,
    7, 0x05, 0x10, 0xA6, 0x06, 0x34, 0x07, 0xFE,
    7, 0x05, 0x10, 0xA8, 0x00, 0xC4, 0x00, 0x01,
    5, 0x03, 0x11, 0xB1, 0x03, 0xD4,
    5, 0x03, 0x11, 0xB2, 0x00, 0x00,
    5, 0x03, 0x11, 0xC4, 0x00, 0x89,
    6, 0x03, 0x02, 0x67, 0x02, 0x85, 0x47,
    5, 0x03, 0x11, 0xB3, 0x01, 0x94,
    5, 0x03, 0x11, 0xBB, 0x00, 0x00,
    5, 0x03, 0x11, 0xBC, 0x00, 0x00,
    5, 0x03, 0x11, 0xBD, 0x07, 0xFF,
    6, 0x03, 0x02, 0x6B, 0x80, 0x00, 0x00,
    6, 0x03, 0x02, 0x6C, 0x7F, 0xFF, 0xFF,
    5, 0x03, 0x11, 0xBF, 0x00, 0x00,
    5, 0x03, 0x11, 0xC0, 0x07, 0xFF,
    6, 0x03, 0x02, 0x6E, 0x80, 0x00, 0x00,
    6, 0x03, 0x02, 0x6F, 0x7F, 0xFF, 0xFF,
    5, 0x03, 0x11, 0xC6, 0x00, 0x73,
    5, 0x03, 0x11, 0xC2, 0x00, 0x1C,
    6, 0x03, 0x02, 0x61, 0x00, 0x20, 0x44,
    5, 0x03, 0x11, 0x87, 0x00, 0x00,
    5, 0x03, 0x11, 0x88, 0x07, 0xFF,
    5, 0x03, 0x11, 0x91, 0x00, 0x00,
    5, 0x03, 0x11, 0xA1, 0x00, 0x07,
    6, 0x03, 0x02, 0x45, 0x00, 0x04, 0x4D,
    5, 0x03, 0x11, 0x95, 0x00, 0x00,
    5, 0x03, 0x11, 0x96, 0x07, 0xFF,
    5, 0x03, 0x11, 0xA0, 0x00, 0x1C,
    6, 0x03, 0x02, 0x42, 0x00, 0x04, 0x4D,
    5, 0x03, 0x11, 0x83, 0x00, 0x00,
    5, 0x03, 0x11, 0x9D, 0x00, 0x00,
    5, 0x03, 0x11, 0x9B, 0x0C, 0x93,
    5, 0x03, 0x11, 0x9C, 0x01, 0xBF,
    5, 0x03, 0x11, 0x9E, 0x00, 0x89,
    6, 0x03, 0x02, 0x3C, 0x00, 0x02, 0x27,
    5, 0x03, 0x11, 0x50, 0x04, 0xC8,
    5, 0x03, 0x11, 0x51, 0x0F, 0x8B,
    5, 0x03, 0x11, 0x5D, 0x00, 0x1C,
    6, 0x03, 0x02, 0x13, 0x00, 0x08, 0x11,
    5, 0x03, 0x11, 0xDA, 0x05, 0x12,
    5, 0x03, 0x11, 0x5D, 0x03, 0xFD,
    6, 0x03, 0x02, 0x13, 0x02, 0x85, 0x47,
    5, 0x03, 0x11, 0x5E, 0x03, 0xFD,
    6, 0x03, 0x02, 0x15, 0x02, 0x85, 0x47,
    5, 0x03, 0x11, 0x53, 0x0E, 0x57,
    5, 0x03, 0x11, 0x54, 0x00, 0xD5,
    6, 0x03, 0x02, 0x17, 0x80, 0x00, 0x00,
    6, 0x03, 0x02, 0x18, 0x7F, 0xFF, 0xFF,
    5, 0x03, 0x11, 0x5B, 0x00, 0x1C,
    6, 0x03, 0x02, 0x0E, 0x00, 0x08, 0x11,
    5, 0x03, 0x11, 0x5B, 0x03, 0xFD,
    6, 0x03, 0x02, 0x0E, 0x02, 0x85, 0x47,
    5, 0x03, 0x11, 0x5C, 0x03, 0xFD,
    6, 0x03, 0x02, 0x10, 0x02, 0x85, 0x47,
    5, 0x03, 0x11, 0x56, 0x0D, 0x80,
    5, 0x03, 0x11, 0x57, 0x01, 0x40,
    5, 0x03, 0x11, 0x60, 0x07, 0xFF,
    6, 0x03, 0x02, 0x1A, 0x80, 0x00, 0x00,
    6, 0x03, 0x02, 0x1B, 0x7F, 0xFF, 0xFF,
    5, 0x03, 0x11, 0x59, 0x00, 0x1C,
    6, 0x03, 0x02, 0x09, 0x00, 0x08, 0x11,
    5, 0x03, 0x11, 0x59, 0x00, 0x1C,
    6, 0x03, 0x02, 0x09, 0x00, 0x08, 0x11,
    5, 0x03, 0x11, 0x5A, 0x00, 0x1C,
    6, 0x03, 0x02, 0x0B, 0x00, 0x08, 0x11,
    5, 0x03, 0x11, 0x98, 0x03, 0xC2,
    5, 0x03, 0x11, 0x99, 0x0F, 0x40,
    5, 0x03, 0x11, 0x9F, 0x00, 0x89,
    6, 0x03, 0x02, 0x3F, 0x00, 0x02, 0x27,
    5, 0x01, 0x10, 0xB3, 0x07, 0xE3,
    5, 0x01, 0x10, 0xB4, 0x00, 0x11,
    5, 0x01, 0x10, 0xB5, 0x01, 0x9A,
    6, 0x01, 0x01, 0xF2, 0x00, 0x00, 0x0A,
    6, 0x01, 0x02, 0x08, 0x00, 0x42, 0x00,
    5, 0x01, 0x10, 0xA6, 0x00, 0x66,
    6, 0x01, 0x01, 0xF0, 0x02, 0x8F, 0x5C,
    6, 0x03, 0x00, 0x81, 0x00, 0xCE, 0xC0,
    6, 0x03, 0x02, 0xEB, 0x00, 0x00, 0x64,
    5, 0x0D, 0x12, 0x5A, 0x04, 0x00,
    5, 0x0D, 0x12, 0x6B, 0x00, 0xCD,
    5, 0x0D, 0x12, 0x7C, 0x05, 0x00,
    6, 0x0D, 0x01, 0x72, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x90, 0x08, 0x8F,
    5, 0x0D, 0x12, 0x8F, 0x00, 0x8F,
    5, 0x0D, 0x12, 0x96, 0x09, 0xB8,
    5, 0x0D, 0x12, 0x95, 0x01, 0xB8,
    5, 0x0D, 0x12, 0xA3, 0x04, 0xF6,
    5, 0x0D, 0x12, 0x5B, 0x03, 0xD7,
    5, 0x0D, 0x12, 0x6C, 0x01, 0x71,
    5, 0x0D, 0x12, 0x7D, 0x05, 0x00,
    6, 0x0D, 0x01, 0x73, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x90, 0x08, 0x8F,
    5, 0x0D, 0x12, 0x8F, 0x00, 0x8F,
    5, 0x0D, 0x12, 0x96, 0x09, 0xB8,
    5, 0x0D, 0x12, 0x95, 0x01, 0xB8,
    5, 0x0D, 0x12, 0xA3, 0x04, 0xF6,
    5, 0x0D, 0x12, 0x5C, 0x03, 0xAE,
    5, 0x0D, 0x12, 0x6D, 0x01, 0x71,
    5, 0x0D, 0x12, 0x7E, 0x05, 0x00,
    6, 0x0D, 0x01, 0x74, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x90, 0x08, 0x8F,
    5, 0x0D, 0x12, 0x8F, 0x00, 0x8F,
    5, 0x0D, 0x12, 0x96, 0x09, 0xB8,
    5, 0x0D, 0x12, 0x95, 0x01, 0xB8,
    5, 0x0D, 0x12, 0xA3, 0x04, 0xF6,
    5, 0x0D, 0x12, 0x5D, 0x03, 0x85,
    5, 0x0D, 0x12, 0x6E, 0x01, 0x71,
    5, 0x0D, 0x12, 0x7F, 0x05, 0x00,
    6, 0x0D, 0x01, 0x75, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x90, 0x08, 0x8F,
    5, 0x0D, 0x12, 0x8F, 0x00, 0x8F,
    5, 0x0D, 0x12, 0x96, 0x09, 0xB8,
    5, 0x0D, 0x12, 0x95, 0x01, 0xB8,
    5, 0x0D, 0x12, 0xA3, 0x04, 0xF6,
    5, 0x0D, 0x12, 0x5E, 0x03, 0x5C,
    5, 0x0D, 0x12, 0x6F, 0x01, 0x71,
    5, 0x0D, 0x12, 0x80, 0x05, 0x00,
    6, 0x0D, 0x01, 0x76, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x90, 0x08, 0x8F,
    5, 0x0D, 0x12, 0x8F, 0x00, 0x8F,
    5, 0x0D, 0x12, 0x96, 0x09, 0xB8,
    5, 0x0D, 0x12, 0x95, 0x01, 0xB8,
    5, 0x0D, 0x12, 0xA3, 0x04, 0xF6,
    5, 0x0D, 0x12, 0x5F, 0x03, 0x33,
    5, 0x0D, 0x12, 0x70, 0x01, 0x71,
    5, 0x0D, 0x12, 0x81, 0x05, 0x00,
    6, 0x0D, 0x01, 0x77, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x90, 0x08, 0x8F,
    5, 0x0D, 0x12, 0x8F, 0x00, 0x8F,
    5, 0x0D, 0x12, 0x96, 0x09, 0xB8,
    5, 0x0D, 0x12, 0x95, 0x01, 0xB8,
    5, 0x0D, 0x12, 0xA3, 0x04, 0xF6,
    5, 0x0D, 0x12, 0x60, 0x03, 0x1F,
    5, 0x0D, 0x12, 0x71, 0x01, 0x71,
    5, 0x0D, 0x12, 0x82, 0x05, 0x00,
    6, 0x0D, 0x01, 0x78, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x90, 0x08, 0x8F,
    5, 0x0D, 0x12, 0x8F, 0x00, 0x8F,
    5, 0x0D, 0x12, 0x96, 0x09, 0xB8,
    5, 0x0D, 0x12, 0x95, 0x01, 0xB8,
    5, 0x0D, 0x12, 0xA3, 0x04, 0xF6,
    5, 0x0D, 0x12, 0x61, 0x02, 0x66,
    5, 0x0D, 0x12, 0x72, 0x01, 0x71,
    5, 0x0D, 0x12, 0x83, 0x05, 0x00,
    6, 0x0D, 0x01, 0x79, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x90, 0x08, 0x8F,
    5, 0x0D, 0x12, 0x8F, 0x00, 0x8F,
    5, 0x0D, 0x12, 0x96, 0x09, 0xB8,
    5, 0x0D, 0x12, 0x95, 0x01, 0xB8,
    5, 0x0D, 0x12, 0xA4, 0x05, 0xAE,
    5, 0x0D, 0x12, 0x62, 0x02, 0x66,
    5, 0x0D, 0x12, 0x73, 0x01, 0x71,
    5, 0x0D, 0x12, 0x84, 0x05, 0x00,
    6, 0x0D, 0x01, 0x7A, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x90, 0x08, 0x8F,
    5, 0x0D, 0x12, 0x8F, 0x00, 0x8F,
    5, 0x0D, 0x12, 0x96, 0x09, 0xB8,
    5, 0x0D, 0x12, 0x95, 0x01, 0xB8,
    5, 0x0D, 0x12, 0xA4, 0x05, 0xAE,
    5, 0x0D, 0x12, 0x63, 0x02, 0x66,
    5, 0x0D, 0x12, 0x74, 0x01, 0x71,
    5, 0x0D, 0x12, 0x85, 0x05, 0x00,
    6, 0x0D, 0x01, 0x7B, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x90, 0x08, 0x8F,
    5, 0x0D, 0x12, 0x8F, 0x00, 0x8F,
    5, 0x0D, 0x12, 0x96, 0x09, 0xB8,
    5, 0x0D, 0x12, 0x95, 0x01, 0xB8,
    5, 0x0D, 0x12, 0xA4, 0x05, 0xAE,
    5, 0x0D, 0x12, 0x64, 0x02, 0x66,
    5, 0x0D, 0x12, 0x75, 0x01, 0x71,
    5, 0x0D, 0x12, 0x86, 0x05, 0x00,
    6, 0x0D, 0x01, 0x7C, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x90, 0x08, 0x8F,
    5, 0x0D, 0x12, 0x8F, 0x00, 0x8F,
    5, 0x0D, 0x12, 0x96, 0x09, 0xB8,
    5, 0x0D, 0x12, 0x95, 0x01, 0xB8,
    5, 0x0D, 0x12, 0xA4, 0x05, 0xAE,
    5, 0x0D, 0x12, 0x65, 0x02, 0x00,
    5, 0x0D, 0x12, 0x76, 0x01, 0x71,
    5, 0x0D, 0x12, 0x87, 0x05, 0x00,
    6, 0x0D, 0x01, 0x7D, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x92, 0x08, 0x1A,
    5, 0x0D, 0x12, 0x91, 0x00, 0x1A,
    5, 0x0D, 0x12, 0x9E, 0x08, 0x3A,
    5, 0x0D, 0x12, 0x9D, 0x00, 0x3A,
    5, 0x0D, 0x12, 0xA4, 0x05, 0xAE,
    5, 0x0D, 0x12, 0x66, 0x02, 0x00,
    5, 0x0D, 0x12, 0x77, 0x01, 0x71,
    5, 0x0D, 0x12, 0x88, 0x05, 0x00,
    6, 0x0D, 0x01, 0x7E, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x92, 0x08, 0x1A,
    5, 0x0D, 0x12, 0x91, 0x00, 0x1A,
    5, 0x0D, 0x12, 0x9E, 0x08, 0x3A,
    5, 0x0D, 0x12, 0x9D, 0x00, 0x3A,
    5, 0x0D, 0x12, 0xA4, 0x05, 0xAE,
    5, 0x0D, 0x12, 0x67, 0x02, 0x00,
    5, 0x0D, 0x12, 0x78, 0x01, 0x71,
    5, 0x0D, 0x12, 0x89, 0x05, 0x00,
    6, 0x0D, 0x01, 0x7F, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x92, 0x08, 0x1A,
    5, 0x0D, 0x12, 0x91, 0x00, 0x1A,
    5, 0x0D, 0x12, 0x9E, 0x08, 0x3A,
    5, 0x0D, 0x12, 0x9D, 0x00, 0x3A,
    5, 0x0D, 0x12, 0xA4, 0x05, 0xAE,
    5, 0x0D, 0x12, 0x68, 0x02, 0x00,
    5, 0x0D, 0x12, 0x79, 0x01, 0x71,
    5, 0x0D, 0x12, 0x8A, 0x05, 0x00,
    6, 0x0D, 0x01, 0x80, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x92, 0x08, 0x1A,
    5, 0x0D, 0x12, 0x91, 0x00, 0x1A,
    5, 0x0D, 0x12, 0x9E, 0x08, 0x3A,
    5, 0x0D, 0x12, 0x9D, 0x00, 0x3A,
    5, 0x0D, 0x12, 0xA4, 0x05, 0xAE,
    5, 0x0D, 0x12, 0x69, 0x02, 0x00,
    5, 0x0D, 0x12, 0x7A, 0x01, 0x71,
    5, 0x0D, 0x12, 0x8B, 0x05, 0x00,
    6, 0x0D, 0x01, 0x81, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x92, 0x08, 0x1A,
    5, 0x0D, 0x12, 0x91, 0x00, 0x1A,
    5, 0x0D, 0x12, 0x9E, 0x08, 0x3A,
    5, 0x0D, 0x12, 0x9D, 0x00, 0x3A,
    5, 0x0D, 0x12, 0xA4, 0x05, 0xAE,
    5, 0x0D, 0x12, 0x6A, 0x02, 0x00,
    5, 0x0D, 0x12, 0x7B, 0x01, 0x71,
    5, 0x0D, 0x12, 0x8C, 0x05, 0x00,
    6, 0x0D, 0x01, 0x82, 0x00, 0xA3, 0xD7,
    5, 0x0D, 0x12, 0x92, 0x08, 0x1A,
    5, 0x0D, 0x12, 0x91, 0x00, 0x1A,
    5, 0x0D, 0x12, 0x9E, 0x08, 0x3A,
    5, 0x0D, 0x12, 0x9D, 0x00, 0x3A,
    5, 0x0D, 0x12, 0xA5, 0x06, 0xA4,
    5, 0x0D, 0x12, 0x98, 0x09, 0x15,
    5, 0x0D, 0x12, 0x97, 0x01, 0x15,
    5, 0x0D, 0x12, 0xA0, 0x08, 0x40,
    5, 0x0D, 0x12, 0x9F, 0x00, 0x40,
    6, 0x0D, 0x01, 0xD8, 0x00, 0x00, 0x00,
    6, 0x0D, 0x01, 0xD9, 0x00, 0x00, 0x00,
    5, 0x0D, 0x12, 0x9A, 0x09, 0x15,
    5, 0x0D, 0x12, 0x99, 0x01, 0x15,
    5, 0x0D, 0x12, 0xA2, 0x08, 0x40,
    5, 0x0D, 0x12, 0xA1, 0x00, 0x40,
    5, 0x0D, 0x12, 0x8E, 0x09, 0x76,
    5, 0x0D, 0x12, 0x8D, 0x01, 0x76,
    6, 0x03, 0x05, 0xE5, 0x0F, 0xE7, 0x00,
    6, 0x05, 0x04, 0xAB, 0x10, 0x05, 0x00,
    6, 0x01, 0x02, 0x77, 0x0C, 0x14, 0x00,
    6, 0x03, 0x05, 0xE4, 0x0F, 0xE6, 0x00,
    6, 0x05, 0x04, 0xAC, 0x1F, 0xF6, 0x00,
    6, 0x01, 0x02, 0x78, 0x0D, 0x46, 0x00,
    6, 0x0D, 0x00, 0x76, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x87, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x77, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x88, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x78, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x89, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x79, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x8A, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x7A, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x7B, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x8B, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x7C, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x8C, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x7D, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x8D, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x7E, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x8E, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x7F, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x8F, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x80, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x90, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x81, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x91, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x82, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x83, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x84, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x85, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x86, 0x7F, 0xFF, 0xFF,
    5, 0x03, 0x11, 0x6A, 0x00, 0x00,
    5, 0x03, 0x11, 0x6B, 0x00, 0x00,
    5, 0x03, 0x11, 0x6C, 0x00, 0x00,
    5, 0x03, 0x11, 0x6D, 0x00, 0x00,
    5, 0x03, 0x11, 0x6E, 0x00, 0x00,
    5, 0x03, 0x11, 0x6F, 0x00, 0x00,
    5, 0x03, 0x11, 0x70, 0x00, 0x00,
    5, 0x03, 0x11, 0x71, 0x00, 0x00,
    5, 0x03, 0x11, 0x72, 0x00, 0x00,
    5, 0x03, 0x11, 0x73, 0x00, 0x00,
    5, 0x03, 0x11, 0x74, 0x00, 0x00,
    5, 0x03, 0x11, 0x75, 0x00, 0x00,
    5, 0x03, 0x11, 0x76, 0x07, 0xFF,
    5, 0x03, 0x11, 0x77, 0x00, 0x00,
    5, 0x03, 0x11, 0x78, 0x00, 0x00,
    5, 0x03, 0x11, 0x79, 0x00, 0x00,
    5, 0x03, 0x11, 0x7A, 0x00, 0x00,
    5, 0x03, 0x11, 0x7B, 0x00, 0x00,
    5, 0x03, 0x11, 0x7C, 0x00, 0x00,
    5, 0x03, 0x11, 0x7D, 0x00, 0x00,
    5, 0x03, 0x11, 0x7E, 0x00, 0x00,
    5, 0x03, 0x11, 0x7F, 0x00, 0x00,
    5, 0x03, 0x11, 0x80, 0x00, 0x00,
    5, 0x03, 0x11, 0x81, 0x00, 0x00,
    5, 0x03, 0x11, 0x82, 0x00, 0x00,
    5, 0x0D, 0x14, 0x8A, 0x01, 0x57,
    5, 0x0D, 0x14, 0x8B, 0x07, 0xF4,
    5, 0x0D, 0x14, 0x8C, 0x02, 0x04,
    5, 0x0D, 0x14, 0x8D, 0x00, 0x0B,
    6, 0x03, 0x00, 0x31, 0x00, 0x00, 0x00,
    6, 0x03, 0x00, 0x34, 0x40, 0x00, 0x00,
    6, 0x03, 0x00, 0x33, 0x06, 0x4D, 0x32,
    6, 0x03, 0x00, 0x2D, 0x00, 0x00, 0x00,
    6, 0x03, 0x00, 0x30, 0x40, 0x00, 0x00,
    6, 0x03, 0x00, 0x2E, 0x00, 0x03, 0xE8,
    6, 0x09, 0x02, 0x1D, 0x00, 0x00, 0x00,
    6, 0x09, 0x02, 0x7A, 0x00, 0x00, 0x01,
    6, 0x09, 0x02, 0x77, 0x40, 0x00, 0x00,
    6, 0x09, 0x02, 0x79, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x3C, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x76, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x87, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x77, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x88, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x78, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x89, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x79, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x8A, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x7A, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x7B, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x8B, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x7C, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x8C, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x7D, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x8D, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x7E, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x8E, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x7F, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x8F, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x80, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x90, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x81, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x91, 0x00, 0x00, 0x00,
    6, 0x0D, 0x00, 0x82, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x83, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x84, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x85, 0x7F, 0xFF, 0xFF,
    6, 0x0D, 0x00, 0x86, 0x7F, 0xFF, 0xFF,
    5, 0x03, 0x10, 0x2E, 0x00, 0x00,
    6, 0x0D, 0x00, 0x00, 0x00, 0x00, 0xC6,
    6, 0x0D, 0x00, 0x01, 0x00, 0x00, 0xC7,
    6, 0x0D, 0x00, 0xC4, 0x00, 0x00, 0x41,
    6, 0x03, 0x00, 0x06, 0x00, 0x00, 0x3C,
    6, 0x03, 0x00, 0x07, 0x00, 0x00, 0x3D,
    6, 0x03, 0x00, 0x08, 0x00, 0x00, 0x3A,
    6, 0x03, 0x00, 0x09, 0x00, 0x00, 0x3B,
    6, 0x09, 0x01, 0x2E, 0x00, 0x01, 0x37,
    5, 0x01, 0x10, 0xA5, 0x02, 0x66,
    9, 0x03, 0x11, 0xDD, 0x07, 0xFF, 0x00, 0x00, 0x00, 0x00,
    5, 0x03, 0x10, 0x38, 0x07, 0xFF,
    6, 0x03, 0x00, 0xEA, 0x04, 0x60, 0x80,
    6, 0x03, 0x00, 0xE8, 0x00, 0x00, 0x32,
    5, 0x03, 0x10, 0xF0, 0x01, 0xD0,
    5, 0x03, 0x10, 0xF1, 0x0F, 0x7E,
    5, 0x03, 0x10, 0xF2, 0x00, 0x41,
    5, 0x03, 0x10, 0xF3, 0x00, 0x46,
    6, 0x03, 0x00, 0xFC, 0x00, 0x20, 0x44,
    5, 0x03, 0x10, 0xE8, 0x00, 0x00,
    5, 0x03, 0x10, 0xE9, 0x07, 0xFF,
    5, 0x03, 0x10, 0xEE, 0x00, 0x07,
    6, 0x03, 0x00, 0xFA, 0x00, 0x04, 0x4D,
    5, 0x03, 0x10, 0xEB, 0x00, 0x00,
    5, 0x03, 0x10, 0xEC, 0x07, 0xFF,
    5, 0x03, 0x10, 0xEF, 0x00, 0x1C,
    5, 0x03, 0x10, 0x2F, 0x00, 0x00,
    5, 0x03, 0x10, 0x30, 0x07, 0xFF,
    5, 0x03, 0x10, 0x32, 0x00, 0x0E,
    6, 0x03, 0x00, 0xE2, 0x00, 0x06, 0x74,
    5, 0x03, 0x11, 0x0E, 0x05, 0x76,
    5, 0x03, 0x11, 0x0D, 0x01, 0x30,
    5, 0x03, 0x11, 0x0C, 0x00, 0x03,
    5, 0x03, 0x11, 0x0B, 0x00, 0xA3,
    5, 0x03, 0x10, 0xE7, 0x03, 0xB3,
    5, 0x01, 0x10, 0x26, 0x00, 0x07,
    5, 0x01, 0x10, 0x21, 0x00, 0x49,
    5, 0x01, 0x10, 0x38, 0x04, 0x00,
    6, 0x03, 0x00, 0xE7, 0xFA, 0x00, 0x00,
    5, 0x03, 0x10, 0x34, 0x00, 0x0A,
    13, 0x03, 0x11, 0x11, 0x0F, 0x42, 0x0D, 0x7B, 0x00, 0xBE, 0x00, 0x00, 0x06, 0x7F,
    5, 0x03, 0x12, 0x70, 0x03, 0x94,
    5, 0x03, 0x12, 0x71, 0x0F, 0x80,
    5, 0x03, 0x12, 0x72, 0x00, 0xCD,
    5, 0x03, 0x12, 0x73, 0x00, 0x46,
    6, 0x03, 0x05, 0xB4, 0x00, 0x06, 0x74,
    5, 0x03, 0x10, 0x20, 0x00, 0x00,
    6, 0x01, 0x0F, 0xF1, 0x7C, 0x90, 0x20,
    6, 0x05, 0x0F, 0xF1, 0xB1, 0x10, 0x40,
    5, 0x03, 0x11, 0xE9, 0x07, 0xFF,
    5, 0x05, 0x10, 0x71, 0x07, 0xFF,
    6, 0x0D, 0x07, 0xDA, 0xFF, 0xFF, 0x12,
    6, 0x0D, 0x07, 0xD9, 0xFF, 0xFF, 0x12,
    0
};

const uint8_t DIRANA_FM[] PROGMEM =
{
    5, 0x0D, 0x14, 0x89, 0x02, 0x00,
    5, 0x0D, 0x14, 0x88, 0x05, 0x73,
    5, 0x03, 0x11, 0xF7, 0x0F, 0xC0,
    6, 0x01, 0x00, 0x01, 0x00, 0x00, 0x52,  // FM mode
    6, 0x05, 0x0F, 0xF1, 0xB1, 0x10, 0x40,
    5, 0x05, 0x10, 0x41, 0x00, 0x02,
    5, 0x01, 0x10, 0x6C, 0x07, 0xFF,
    5, 0x01, 0x10, 0x6D, 0x0F, 0x6B,
    5, 0x01, 0x10, 0x6E, 0x00, 0x00,
    5, 0x01, 0x10, 0x6F, 0x04, 0x00,
    5, 0x01, 0x10, 0x70, 0x01, 0x9A,
    5, 0x01, 0x10, 0x71, 0x00, 0x00,
    5, 0x01, 0x10, 0x76, 0x00, 0x03,
    5, 0x01, 0x10, 0x77, 0x00, 0x00,
    5, 0x01, 0x10, 0x78, 0x00, 0x02,
    5, 0x01, 0x10, 0x79, 0x00, 0x03,
    5, 0x01, 0x10, 0x7F, 0x00, 0x00,
    6, 0x01, 0x01, 0xAD, 0x0F, 0xC0, 0xFC,
    6, 0x01, 0x01, 0xAF, 0x00, 0x00, 0x00,
    6, 0x05, 0x00, 0x67, 0x00, 0x00, 0x0C,
    5, 0x03, 0x11, 0xF6, 0x00, 0xE9,
    5, 0x03, 0x10, 0x2E, 0x00, 0x00,
    6, 0x0D, 0x00, 0x00, 0x00, 0x00, 0xC6,
    6, 0x0D, 0x00, 0x01, 0x00, 0x00, 0xC7,
    6, 0x0D, 0x00, 0xC4, 0x00, 0x00, 0x41,
    6, 0x03, 0x00, 0x06, 0x00, 0x00, 0x38,
    6, 0x03, 0x00, 0x07, 0x00, 0x00, 0x39,
    6, 0x03, 0x00, 0x08, 0x00, 0x00, 0x3A,
    6, 0x03, 0x00, 0x09, 0x00, 0x00, 0x3B,
    6, 0x09, 0x01, 0x2E, 0x00, 0x01, 0x37,
    6, 0x01, 0x0F, 0xF5, 0xE2, 0x76, 0x28,
    6, 0x05, 0x0F, 0xF5, 0xE2, 0x50, 0x8D,
//  6, 0x05, 0x0F, 0xF9, 0x00, 0x00, 0x17,
//  5, 0x03, 0x10, 0x11, 0x06, 0x5A,  // audio volume scaler
//  5, 0x00, 0x00, 0x34, 0x10, 0x20,  // RDS sync mode - no error corr.
    5, 0x00, 0x00, 0x34, 0x30, 0x20,  // RDS sync mode - max 2 bits error correctable blocks are handled as valid
    0
};

const uint8_t DIRANA_AM[] PROGMEM =
{
    5, 0x0D, 0x14, 0x89, 0x02, 0x00,
    6, 0x01, 0x00, 0x01, 0x00, 0x00, 0x51,  // AM mode
//  6, 0x05, 0x0F, 0xF9, 0x00, 0x00, 0x1F,
    5, 0x05, 0x10, 0x41, 0x00, 0x02,
//  6, 0x01, 0x00, 0xE7, 0x00, 0x00, 0x01, // FIR selection
//  6, 0x01, 0x00, 0xE8, 0x00, 0x00, 0x02, // FIR attenuation
//  6, 0x05, 0x02, 0x1B, 0x00, 0x00, 0x01, // FIR selection
//  6, 0x05, 0x02, 0x1C, 0x00, 0x00, 0x02, // FIR attenuation
    5, 0x03, 0x11, 0xF6, 0x00, 0xE9,
    5, 0x03, 0x10, 0x2E, 0x07, 0xFF,
    6, 0x0D, 0x00, 0x00, 0x00, 0x00, 0xC6,
    6, 0x0D, 0x00, 0x01, 0x00, 0x00, 0xC7,
    6, 0x0D, 0x00, 0xC4, 0x00, 0x00, 0x03,
    6, 0x03, 0x00, 0x06, 0x00, 0x00, 0x3C,
    6, 0x03, 0x00, 0x07, 0x00, 0x00, 0x3D,
    6, 0x09, 0x01, 0x2E, 0x00, 0x02, 0x1A,
    6, 0x01, 0x0F, 0xF5, 0xE2, 0x76, 0x28,
    6, 0x05, 0x0F, 0xF5, 0xE2, 0x75, 0xC1,
    5, 0x03, 0x11, 0x27, 0x0F, 0xEC,
//  5, 0x03, 0x10, 0x11, 0x07, 0x21,  // audio volume scaler
    0
};

const uint8_t DIRANA_DEEMPHASIS_50US[] PROGMEM =
{
    9, 0x03, 0x11, 0xD7, 0x02, 0xC0, 0x04, 0xE4, 0x00, 0x85,
    0
};

const uint8_t DIRANA_DEEMPHASIS_75US[] PROGMEM =
{
    9, 0x03, 0x11, 0xD7, 0x01, 0xF6, 0x05, 0xC3, 0x00, 0x85,
    0
};

const uint8_t DIRANA_DEEMPHASIS_0US[] PROGMEM =
{
    9, 0x03, 0x11, 0xD7, 0x07, 0xFF, 0x00, 0x00, 0x00, 0x00,
    0
};

#define DIRANA_FILTER_ADAPTIVE_COUNT 16
#define DIRANA_FILTER_COEFF_LENGTH   64

const uint8_t DIRANA_FILTER_COEFF[] PROGMEM =
{
    /*  0: NFM  */ 0x00, 0x01, 0x00, 0x03, 0x00, 0x06, 0x00, 0x0A, 0x00, 0x11, 0x00, 0x1A, 0x00, 0x26, 0x00, 0x36, 0x00, 0x4A, 0x00, 0x63, 0x00, 0x81, 0x00, 0xA5, 0x00, 0xCF, 0x00, 0xFF, 0x01, 0x35, 0x01, 0x71, 0x01, 0xB3, 0x01, 0xFA, 0x02, 0x45, 0x02, 0x93, 0x02, 0xE4, 0x03, 0x35, 0x03, 0x86, 0x03, 0xD5, 0x04, 0x20, 0x04, 0x66, 0x04, 0xA5, 0x04, 0xDC, 0x05, 0x0A, 0x05, 0x2D, 0x05, 0x45, 0x05, 0x51,
    /*  1: AM 0 */ 0xFF, 0xFE, 0xFF, 0xFB, 0xFF, 0xF7, 0xFF, 0xF1, 0xFF, 0xE9, 0xFF, 0xE0, 0xFF, 0xD5, 0xFF, 0xCA, 0xFF, 0xBE, 0xFF, 0xB5, 0xFF, 0xAF, 0xFF, 0xAF, 0xFF, 0xB6, 0xFF, 0xC8, 0xFF, 0xE7, 0x00, 0x15, 0x00, 0x54, 0x00, 0xA5, 0x01, 0x09, 0x01, 0x7F, 0x02, 0x07, 0x02, 0x9C, 0x03, 0x3E, 0x03, 0xE6, 0x04, 0x90, 0x05, 0x36, 0x05, 0xD3, 0x06, 0x61, 0x06, 0xDA, 0x07, 0x39, 0x07, 0x7B, 0x07, 0x9C,
    /*  2: AM 1 */ 0x00, 0x02, 0x00, 0x01, 0xFF, 0xFF, 0xFF, 0xFB, 0xFF, 0xF5, 0xFF, 0xEB, 0xFF, 0xDE, 0xFF, 0xCF, 0xFF, 0xBD, 0xFF, 0xA9, 0xFF, 0x97, 0xFF, 0x87, 0xFF, 0x7D, 0xFF, 0x7D, 0xFF, 0x89, 0xFF, 0xA6, 0xFF, 0xD8, 0x00, 0x20, 0x00, 0x81, 0x00, 0xFC, 0x01, 0x91, 0x02, 0x3C, 0x02, 0xFA, 0x03, 0xC7, 0x04, 0x9A, 0x05, 0x6D, 0x06, 0x37, 0x06, 0xF0, 0x07, 0x90, 0x08, 0x0E, 0x08, 0x66, 0x08, 0x93,
    /*  3: AM 2 */ 0x00, 0x05, 0x00, 0x07, 0x00, 0x09, 0x00, 0x09, 0x00, 0x07, 0x00, 0x02, 0xFF, 0xF8, 0xFF, 0xE9, 0xFF, 0xD5, 0xFF, 0xBC, 0xFF, 0x9E, 0xFF, 0x7F, 0xFF, 0x61, 0xFF, 0x49, 0xFF, 0x3B, 0xFF, 0x3D, 0xFF, 0x54, 0xFF, 0x87, 0xFF, 0xDA, 0x00, 0x50, 0x00, 0xE9, 0x01, 0xA6, 0x02, 0x82, 0x03, 0x77, 0x04, 0x7D, 0x05, 0x89, 0x06, 0x8F, 0x07, 0x82, 0x08, 0x57, 0x09, 0x01, 0x09, 0x78, 0x09, 0xB6,
    /*  4: AM 3 */ 0x00, 0x04, 0x00, 0x08, 0x00, 0x0C, 0x00, 0x12, 0x00, 0x16, 0x00, 0x1A, 0x00, 0x19, 0x00, 0x13, 0x00, 0x06, 0xFF, 0xF0, 0xFF, 0xD1, 0xFF, 0xA9, 0xFF, 0x7A, 0xFF, 0x49, 0xFF, 0x1B, 0xFE, 0xF7, 0xFE, 0xE7, 0xFE, 0xF2, 0xFF, 0x22, 0xFF, 0x7F, 0x00, 0x0D, 0x00, 0xCE, 0x01, 0xC1, 0x02, 0xDF, 0x04, 0x1E, 0x05, 0x70, 0x06, 0xC3, 0x08, 0x06, 0x09, 0x24, 0x0A, 0x0D, 0x0A, 0xB1, 0x0B, 0x06,
    /*  5: AM 4 */ 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x04, 0x00, 0x0B, 0x00, 0x14, 0x00, 0x1F, 0x00, 0x2B, 0x00, 0x34, 0x00, 0x38, 0x00, 0x34, 0x00, 0x23, 0x00, 0x03, 0xFF, 0xD3, 0xFF, 0x94, 0xFF, 0x4C, 0xFF, 0x01, 0xFE, 0xBE, 0xFE, 0x90, 0xFE, 0x85, 0xFE, 0xAD, 0xFF, 0x13, 0xFF, 0xBF, 0x00, 0xB5, 0x01, 0xF1, 0x03, 0x68, 0x05, 0x09, 0x06, 0xBC, 0x08, 0x65, 0x09, 0xE6, 0x0B, 0x25, 0x0C, 0x08, 0x0C, 0x7E,
    /*  6: AM 5 */ 0xFF, 0xFB, 0xFF, 0xF8, 0xFF, 0xF6, 0xFF, 0xF7, 0xFF, 0xFC, 0x00, 0x06, 0x00, 0x16, 0x00, 0x2B, 0x00, 0x42, 0x00, 0x57, 0x00, 0x65, 0x00, 0x64, 0x00, 0x4F, 0x00, 0x20, 0xFF, 0xD7, 0xFF, 0x76, 0xFF, 0x07, 0xFE, 0x99, 0xFE, 0x40, 0xFE, 0x13, 0xFE, 0x2A, 0xFE, 0x98, 0xFF, 0x6C, 0x00, 0xAB, 0x02, 0x4C, 0x04, 0x3E, 0x06, 0x60, 0x08, 0x8A, 0x0A, 0x90, 0x0C, 0x45, 0x0D, 0x82, 0x0E, 0x27,
    /*  7: AM 6 */ 0xFF, 0xFF, 0xFF, 0xFC, 0xFF, 0xF6, 0xFF, 0xEF, 0xFF, 0xE8, 0xFF, 0xE3, 0xFF, 0xE6, 0xFF, 0xF2, 0x00, 0x09, 0x00, 0x2C, 0x00, 0x56, 0x00, 0x80, 0x00, 0x9F, 0x00, 0xA6, 0x00, 0x8A, 0x00, 0x42, 0xFF, 0xCF, 0xFF, 0x39, 0xFE, 0x95, 0xFE, 0x00, 0xFD, 0x9F, 0xFD, 0x99, 0xFE, 0x11, 0xFF, 0x1E, 0x00, 0xC5, 0x02, 0xF8, 0x05, 0x92, 0x08, 0x5C, 0x0B, 0x11, 0x0D, 0x6A, 0x0F, 0x25, 0x10, 0x10,
    /*  8: AM 7 */ 0x00, 0x05, 0x00, 0x08, 0x00, 0x08, 0x00, 0x04, 0xFF, 0xFA, 0xFF, 0xEB, 0xFF, 0xD9, 0xFF, 0xC8, 0xFF, 0xC1, 0xFF, 0xCA, 0xFF, 0xE8, 0x00, 0x1E, 0x00, 0x65, 0x00, 0xAF, 0x00, 0xE8, 0x00, 0xF9, 0x00, 0xCB, 0x00, 0x56, 0xFF, 0x9C, 0xFE, 0xB7, 0xFD, 0xD2, 0xFD, 0x2A, 0xFC, 0xFD, 0xFD, 0x87, 0xFE, 0xEC, 0x01, 0x31, 0x04, 0x35, 0x07, 0xB2, 0x0B, 0x45, 0x0E, 0x7C, 0x10, 0xEA, 0x12, 0x38,
    /*  9: AM 8 */ 0xFF, 0xFE, 0x00, 0x02, 0x00, 0x08, 0x00, 0x11, 0x00, 0x19, 0x00, 0x1A, 0x00, 0x11, 0xFF, 0xFA, 0xFF, 0xD7, 0xFF, 0xB0, 0xFF, 0x93, 0xFF, 0x8F, 0xFF, 0xB3, 0x00, 0x02, 0x00, 0x74, 0x00, 0xEE, 0x01, 0x48, 0x01, 0x58, 0x00, 0xFD, 0x00, 0x2C, 0xFE, 0xFF, 0xFD, 0xB5, 0xFC, 0xAE, 0xFC, 0x53, 0xFD, 0x06, 0xFE, 0xFE, 0x02, 0x36, 0x06, 0x65, 0x0B, 0x03, 0x0F, 0x5F, 0x12, 0xC6, 0x14, 0xA3,
    /* 10: FM 0 */ 0xFF, 0xFD, 0xFF, 0xF9, 0xFF, 0xF4, 0xFF, 0xF4, 0xFF, 0xFA, 0x00, 0x0A, 0x00, 0x20, 0x00, 0x34, 0x00, 0x3A, 0x00, 0x27, 0xFF, 0xF6, 0xFF, 0xB1, 0xFF, 0x6F, 0xFF, 0x4F, 0xFF, 0x6E, 0xFF, 0xDA, 0x00, 0x82, 0x01, 0x35, 0x01, 0xAB, 0x01, 0x9C, 0x00, 0xDD, 0xFF, 0x7C, 0xFD, 0xCC, 0xFC, 0x5D, 0xFB, 0xDC, 0xFC, 0xDE, 0xFF, 0xAF, 0x04, 0x2B, 0x09, 0xB3, 0x0F, 0x4E, 0x13, 0xDF, 0x16, 0x6F,
    /* 11: AM10 */ 0x00, 0x06, 0x00, 0x08, 0x00, 0x06, 0xFF, 0xFC, 0xFF, 0xED, 0xFF, 0xDE, 0xFF, 0xDB, 0xFF, 0xEE, 0x00, 0x17, 0x00, 0x4B, 0x00, 0x70, 0x00, 0x67, 0x00, 0x21, 0xFF, 0xA8, 0xFF, 0x2A, 0xFE, 0xE9, 0xFF, 0x21, 0xFF, 0xE3, 0x00, 0xFE, 0x02, 0x01, 0x02, 0x5D, 0x01, 0xA7, 0xFF, 0xD4, 0xFD, 0x64, 0xFB, 0x52, 0xFA, 0xCA, 0xFC, 0xBD, 0x01, 0x77, 0x08, 0x62, 0x10, 0x15, 0x16, 0xB6, 0x1A, 0x8B,
    /* 12: FM 1 */ 0xFF, 0xFB, 0xFF, 0xFA, 0xFF, 0xFF, 0x00, 0x0B, 0x00, 0x17, 0x00, 0x1A, 0x00, 0x09, 0xFF, 0xE5, 0xFF, 0xC0, 0xFF, 0xB5, 0xFF, 0xDB, 0x00, 0x2F, 0x00, 0x8A, 0x00, 0xAF, 0x00, 0x6C, 0xFF, 0xC4, 0xFF, 0x00, 0xFE, 0x99, 0xFE, 0xF8, 0x00, 0x28, 0x01, 0xB0, 0x02, 0xB0, 0x02, 0x4E, 0x00, 0x44, 0xFD, 0x3A, 0xFA, 0xBA, 0xFA, 0x9D, 0xFE, 0x30, 0x05, 0x6D, 0x0E, 0xBD, 0x17, 0x6B, 0x1C, 0xA6,
    /* 13: AM12 */ 0x00, 0x06, 0x00, 0x05, 0xFF, 0xFC, 0xFF, 0xEE, 0xFF, 0xE8, 0xFF, 0xF5, 0x00, 0x17, 0x00, 0x3A, 0x00, 0x3D, 0x00, 0x09, 0xFF, 0xB1, 0xFF, 0x72, 0xFF, 0x8E, 0x00, 0x1A, 0x00, 0xCC, 0x01, 0x1F, 0x00, 0xA6, 0xFF, 0x75, 0xFE, 0x40, 0xFE, 0x03, 0xFF, 0x4B, 0x01, 0x9E, 0x03, 0x82, 0x03, 0x4A, 0x00, 0x50, 0xFB, 0xD3, 0xF8, 0xBB, 0xFA, 0x2F, 0x01, 0xAB, 0x0D, 0xBA, 0x1A, 0x37, 0x22, 0x24,
    /* 14: FM 2 */ 0xFF, 0xFD, 0x00, 0x03, 0x00, 0x0C, 0x00, 0x0E, 0x00, 0x01, 0xFF, 0xE8, 0xFF, 0xD8, 0xFF, 0xEA, 0x00, 0x1F, 0x00, 0x52, 0x00, 0x4B, 0xFF, 0xF5, 0xFF, 0x7F, 0xFF, 0x54, 0xFF, 0xBE, 0x00, 0x95, 0x01, 0x34, 0x00, 0xEB, 0xFF, 0xA6, 0xFE, 0x3C, 0xFD, 0xF5, 0xFF, 0x82, 0x02, 0x1C, 0x03, 0xB7, 0x02, 0x6E, 0xFE, 0x3F, 0xF9, 0xCE, 0xF9, 0x31, 0xFF, 0x57, 0x0B, 0x9F, 0x19, 0x7E, 0x22, 0xA8,
    /* 15: AM13 */ 0xFF, 0xFA, 0xFF, 0xF9, 0x00, 0x01, 0x00, 0x11, 0x00, 0x19, 0x00, 0x06, 0xFF, 0xDE, 0xFF, 0xC3, 0xFF, 0xDE, 0x00, 0x2F, 0x00, 0x78, 0x00, 0x63, 0xFF, 0xD9, 0xFF, 0x3A, 0xFF, 0x26, 0xFF, 0xED, 0x01, 0x19, 0x01, 0x9A, 0x00, 0xA9, 0xFE, 0xB1, 0xFD, 0x53, 0xFE, 0x28, 0x01, 0x2B, 0x04, 0x25, 0x04, 0x15, 0xFF, 0xCF, 0xF9, 0xC6, 0xF7, 0x3A, 0xFC, 0xC5, 0x0A, 0x99, 0x1B, 0x56, 0x26, 0xBD,
    /* 16: FM 3 */ 0x00, 0x02, 0x00, 0x08, 0x00, 0x06, 0xFF, 0xF7, 0xFF, 0xE9, 0xFF, 0xF3, 0x00, 0x19, 0x00, 0x34, 0x00, 0x14, 0xFF, 0xC6, 0xFF, 0x9D, 0xFF, 0xE7, 0x00, 0x75, 0x00, 0xAA, 0x00, 0x15, 0xFF, 0x26, 0xFE, 0xF1, 0x00, 0x04, 0x01, 0x7B, 0x01, 0x9C, 0xFF, 0xBC, 0xFD, 0x7F, 0xFD, 0x9B, 0x00, 0xD2, 0x04, 0x46, 0x03, 0xA6, 0xFD, 0xE5, 0xF7, 0xE3, 0xF9, 0x79, 0x06, 0x7F, 0x19, 0xF6, 0x28, 0x6D,
    /* 17: FM 4 */ 0x00, 0x05, 0x00, 0x03, 0xFF, 0xF7, 0xFF, 0xF1, 0x00, 0x04, 0x00, 0x1F, 0x00, 0x12, 0xFF, 0xDB, 0xFF, 0xC5, 0x00, 0x0C, 0x00, 0x65, 0x00, 0x3C, 0xFF, 0x98, 0xFF, 0x59, 0x00, 0x19, 0x00, 0xFF, 0x00, 0x9C, 0xFF, 0x10, 0xFE, 0x77, 0x00, 0x28, 0x02, 0x3A, 0x01, 0x6E, 0xFD, 0xFE, 0xFC, 0x86, 0x00, 0x37, 0x05, 0x22, 0x03, 0xA6, 0xFA, 0xFA, 0xF5, 0xCA, 0x00, 0x40, 0x18, 0xCE, 0x2D, 0xE9,
    /* 18: FM 5 */ 0x00, 0x03, 0xFF, 0xFB, 0xFF, 0xF6, 0x00, 0x07, 0x00, 0x16, 0xFF, 0xFC, 0xFF, 0xD7, 0xFF, 0xF7, 0x00, 0x3F, 0x00, 0x27, 0xFF, 0xAD, 0xFF, 0xA7, 0x00, 0x5A, 0x00, 0xA3, 0xFF, 0xBA, 0xFE, 0xFF, 0x00, 0x05, 0x01, 0x68, 0x00, 0x7A, 0xFE, 0x3C, 0xFE, 0xB7, 0x01, 0xF8, 0x02, 0x7A, 0xFE, 0x2A, 0xFB, 0xDC, 0x01, 0x1C, 0x06, 0x85, 0x00, 0xCB, 0xF5, 0x84, 0xF9, 0xF5, 0x16, 0x16, 0x33, 0x13,
    /* 19: FM 6 */ 0xFF, 0xFE, 0xFF, 0xF8, 0x00, 0x04, 0x00, 0x10, 0xFF, 0xF7, 0xFF, 0xE3, 0x00, 0x10, 0x00, 0x30, 0xFF, 0xE3, 0xFF, 0xB5, 0x00, 0x2F, 0x00, 0x6F, 0xFF, 0xB7, 0xFF, 0x60, 0x00, 0x6F, 0x00, 0xDF, 0xFF, 0x5C, 0xFE, 0xCF, 0x00, 0xEE, 0x01, 0x9C, 0xFE, 0xAC, 0xFD, 0xD6, 0x01, 0xE7, 0x02, 0xF0, 0xFD, 0x3A, 0xFB, 0xE5, 0x04, 0x3C, 0x06, 0x2E, 0xF8, 0xBF, 0xF4, 0xB6, 0x11, 0xFB, 0x37, 0xE1,
    /* 20: FM 7 */ 0xFF, 0xFB, 0x00, 0x00, 0x00, 0x0C, 0xFF, 0xFB, 0xFF, 0xEB, 0x00, 0x13, 0x00, 0x1B, 0xFF, 0xD3, 0xFF, 0xE9, 0x00, 0x52, 0xFF, 0xFF, 0xFF, 0x85, 0x00, 0x38, 0x00, 0x9C, 0xFF, 0x6C, 0xFF, 0x63, 0x01, 0x14, 0x00, 0x66, 0xFE, 0x57, 0x00, 0x28, 0x02, 0x37, 0xFE, 0xD8, 0xFD, 0x72, 0x02, 0xB2, 0x02, 0x6E, 0xFB, 0x16, 0xFE, 0x9A, 0x08, 0x37, 0xFE, 0x67, 0xF1, 0x6A, 0x0C, 0xBF, 0x3C, 0x4A,
    /* 21: FM 8 */ 0xFF, 0xFC, 0x00, 0x07, 0x00, 0x01, 0xFF, 0xEF, 0x00, 0x0D, 0x00, 0x12, 0xFF, 0xD9, 0x00, 0x04, 0x00, 0x3D, 0xFF, 0xC6, 0xFF, 0xD4, 0x00, 0x7D, 0xFF, 0xDA, 0xFF, 0x69, 0x00, 0xB3, 0x00, 0x46, 0xFE, 0xCC, 0x00, 0x97, 0x01, 0x30, 0xFE, 0x3C, 0xFF, 0xC9, 0x02, 0x99, 0xFE, 0x34, 0xFD, 0xD9, 0x04, 0x46, 0xFF, 0x8E, 0xFA, 0x13, 0x05, 0xCC, 0x04, 0x97, 0xF0, 0xA5, 0x06, 0xB5, 0x40, 0x48,
    /* 22: FM 9 */ 0x00, 0x01, 0x00, 0x06, 0xFF, 0xF5, 0x00, 0x03, 0x00, 0x12, 0xFF, 0xE3, 0x00, 0x07, 0x00, 0x29, 0xFF, 0xC2, 0x00, 0x0C, 0x00, 0x51, 0xFF, 0x8C, 0x00, 0x13, 0x00, 0x93, 0xFF, 0x37, 0x00, 0x1C, 0x00, 0xFB, 0xFE, 0xB5, 0x00, 0x25, 0x01, 0x9C, 0xFD, 0xF0, 0x00, 0x2E, 0x02, 0xA2, 0xFC, 0xAD, 0x00, 0x37, 0x04, 0x80, 0xFA, 0x3C, 0x00, 0x3D, 0x09, 0x3F, 0xF2, 0x87, 0x00, 0x40, 0x43, 0xD2,
    /* 23: FM10 */ 0x00, 0x05, 0xFF, 0xFD, 0xFF, 0xFA, 0x00, 0x11, 0xFF, 0xEF, 0xFF, 0xFD, 0x00, 0x22, 0xFF, 0xCE, 0x00, 0x1A, 0x00, 0x27, 0xFF, 0x9B, 0x00, 0x61, 0x00, 0x00, 0xFF, 0x72, 0x00, 0xD7, 0xFF, 0x87, 0xFF, 0x87, 0x01, 0x5D, 0xFE, 0xA0, 0x00, 0x28, 0x01, 0xA7, 0xFD, 0x55, 0x01, 0xB0, 0x01, 0x2E, 0xFB, 0xDC, 0x04, 0x9E, 0xFE, 0xF4, 0xFA, 0x8D, 0x0A, 0xD0, 0xF6, 0xBD, 0xF9, 0xC7, 0x46, 0xE2,
    /* 24: FM11 */ 0x00, 0x04, 0xFF, 0xF8, 0x00, 0x08, 0xFF, 0xFF, 0xFF, 0xF2, 0x00, 0x1F, 0xFF, 0xDD, 0x00, 0x11, 0x00, 0x19, 0xFF, 0xB5, 0x00, 0x63, 0xFF, 0xBA, 0xFF, 0xED, 0x00, 0x89, 0xFF, 0x25, 0x00, 0xC6, 0xFF, 0xD4, 0xFF, 0x3A, 0x01, 0x9B, 0xFE, 0x3C, 0x00, 0xE9, 0x00, 0xD1, 0xFD, 0x4C, 0x03, 0xAB, 0xFD, 0x3A, 0xFF, 0xC7, 0x04, 0x80, 0xF7, 0xD1, 0x08, 0xC4, 0xFC, 0x8D, 0xF3, 0xB2, 0x49, 0x73,
    /* 25: FM12 */ 0x00, 0x00, 0xFF, 0xFC, 0x00, 0x0A, 0xFF, 0xEF, 0x00, 0x14, 0xFF, 0xF2, 0xFF, 0xFC, 0x00, 0x1F, 0xFF, 0xC2, 0x00, 0x52, 0xFF, 0xB3, 0x00, 0x25, 0x00, 0x26, 0xFF, 0x7C, 0x00, 0xD6, 0xFF, 0x09, 0x00, 0xC4, 0xFF, 0xCF, 0xFF, 0x54, 0x01, 0x9C, 0xFD, 0xB6, 0x02, 0x5C, 0xFE, 0x6D, 0xFF, 0xE4, 0x02, 0x6E, 0xFB, 0x2D, 0x06, 0x7D, 0xF9, 0x85, 0x03, 0xCA, 0x02, 0xF6, 0xEE, 0x63, 0x4B, 0x7F,
    /* 26: FM13 */ 0xFF, 0xFB, 0x00, 0x05, 0xFF, 0xFC, 0xFF, 0xFF, 0x00, 0x0A, 0xFF, 0xE9, 0x00, 0x26, 0xFF, 0xCC, 0x00, 0x3D, 0xFF, 0xC6, 0x00, 0x27, 0xFF, 0xFF, 0xFF, 0xC8, 0x00, 0x7F, 0xFF, 0x38, 0x01, 0x05, 0xFE, 0xDD, 0x01, 0x11, 0xFF, 0x41, 0x00, 0x28, 0x00, 0xAF, 0xFE, 0x4C, 0x02, 0xC5, 0xFC, 0x4D, 0x04, 0x46, 0xFB, 0xC1, 0x03, 0x59, 0xFE, 0xBC, 0xFD, 0x8C, 0x08, 0xDC, 0xEA, 0x2F, 0x4D, 0x05,
    /* 27: FM14 */ 0xFF, 0xFB, 0x00, 0x08, 0xFF, 0xF4, 0x00, 0x11, 0xFF, 0xEA, 0x00, 0x1A, 0xFF, 0xE3, 0x00, 0x1D, 0xFF, 0xE9, 0x00, 0x0C, 0x00, 0x07, 0xFF, 0xDC, 0x00, 0x49, 0xFF, 0x87, 0x00, 0xB1, 0xFF, 0x12, 0x01, 0x2E, 0xFE, 0x94, 0x01, 0xA0, 0xFE, 0x3C, 0x01, 0xD0, 0xFE, 0x47, 0x01, 0x75, 0xFF, 0x07, 0x00, 0x37, 0x00, 0xE4, 0xFD, 0x8F, 0x04, 0xA3, 0xF8, 0x20, 0x0D, 0x3A, 0xE7, 0x5A, 0x4E, 0x00,
    /* 28: FM15 */ 0x00, 0x00, 0x00, 0x01, 0xFF, 0xFE, 0x00, 0x03, 0xFF, 0xFA, 0x00, 0x09, 0xFF, 0xF2, 0x00, 0x14, 0xFF, 0xE4, 0x00, 0x27, 0xFF, 0xCC, 0x00, 0x45, 0xFF, 0xA6, 0x00, 0x73, 0xFF, 0x6E, 0x00, 0xB6, 0xFF, 0x1E, 0x01, 0x16, 0xFE, 0xAD, 0x01, 0x9C, 0xFE, 0x0E, 0x02, 0x59, 0xFD, 0x2C, 0x03, 0x6A, 0xFB, 0xDC, 0x05, 0x12, 0xF9, 0xB3, 0x08, 0x07, 0xF5, 0x56, 0x0F, 0x4E, 0xE6, 0x12, 0x4E, 0x6F
};

const uint8_t DIRANA_FILTER_ADAPTIVE[DIRANA_FILTER_ADAPTIVE_COUNT] PROGMEM =
{
    10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25
};

const uint16_t DIRANA_BANDWIDTH_FM[] PROGMEM
{
    /*  0: NFM  */ 90,
    /*  1: AM 0 */ 150,
    /*  2: AM 1 */ 170,
    /*  3: AM 2 */ 200,
    /*  4: AM 3 */ 240,
    /*  5: AM 4 */ 270,
    /*  6: AM 5 */ 320,
    /*  7: AM 6 */ 360,
    /*  8: AM 7 */ 420,
    /*  9: AM 8 */ 480,
    /* 10: FM 0 */ 550,
    /* 11: AM10 */ 630,
    /* 12: FM 1 */ 730,
    /* 13: AM12 */ 830,
    /* 14: FM 2 */ 900,
    /* 15: AM13 */ 950,
    /* 16: FM 3 */ 1080,
    /* 17: FM 4 */ 1250,
    /* 18: FM 5 */ 1420,
    /* 19: FM 6 */ 1590,
    /* 20: FM 7 */ 1770,
    /* 21: FM 8 */ 1940,
    /* 22: FM 9 */ 2110,
    /* 23: FM10 */ 2290,
    /* 24: FM11 */ 2460,
    /* 25: FM12 */ 2630,
    /* 26: FM13 */ 2810,
    /* 27: FM14 */ 2980,
    /* 28: FM15 */ 3090,
    0
};

const uint16_t DIRANA_BANDWIDTH_AM[] PROGMEM
{
    /*  0: NFM  */ 11,
    /*  1: AM 0 */ 19,
    /*  2: AM 1 */ 22,
    /*  3: AM 2 */ 25,
    /*  4: AM 3 */ 29,
    /*  5: AM 4 */ 34,
    /*  6: AM 5 */ 39,
    /*  7: AM 6 */ 46,
    /*  8: AM 7 */ 52,
    /*  9: AM 8 */ 60,
    /* 10: FM 0 */ 69,
    /* 11: AM10 */ 79,
    /* 12: FM 1 */ 91,
    /* 13: AM12 */ 104,
    /* 14: FM 2 */ 113,
    /* 15: AM13 */ 118,
    /* 16: FM 3 */ 135,
    /* 17: FM 4 */ 156,
    /* 18: FM 5 */ 178,
    /* 19: FM 6 */ 199,
    /* 20: FM 7 */ 221,
    /* 21: FM 8 */ 243,
    /* 22: FM 9 */ 264,
    /* 23: FM10 */ 286,
    /* 24: FM11 */ 308,
    /* 25: FM12 */ 329,
    /* 26: FM13 */ 351,
    /* 27: FM14 */ 373,
    /* 28: FM15 */ 386,
    0
};

#endif
